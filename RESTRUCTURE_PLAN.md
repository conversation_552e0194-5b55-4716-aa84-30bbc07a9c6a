# 🏗️ Lemur AI - Clean Architecture Restructure Plan

## Current Issues
- ❌ Duplicate code between `backend/` and `Backend_Lemur_Waitlist/`
- ❌ Inconsistent imports and dependencies
- ❌ Mixed responsibilities in files
- ❌ Unclear separation of concerns
- ❌ Multiple .env files with different configs

## 🎯 New Clean Structure

```
lemur-ai/
├── 📁 backend/                     # Main API Server
│   ├── 📁 app/                     # Application core
│   │   ├── 📁 api/                 # API routes
│   │   │   ├── __init__.py
│   │   │   ├── auth.py             # Authentication routes
│   │   │   ├── calendar.py         # Google Calendar routes
│   │   │   ├── clients.py          # Client management routes
│   │   │   ├── files.py            # File upload routes
│   │   │   ├── ai.py               # AI generation routes
│   │   │   └── bots.py             # Recall AI bot routes
│   │   ├── 📁 core/                # Core business logic
│   │   │   ├── __init__.py
│   │   │   ├── auth.py             # Authentication logic
│   │   │   ├── database.py         # Database operations
│   │   │   ├── file_processor.py   # Document processing
│   │   │   ├── ai_service.py       # AI/LLM service
│   │   │   ├── vector_store.py     # ChromaDB operations
│   │   │   └── recall_service.py   # Recall AI integration
│   │   ├── 📁 models/              # Data models
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── client.py
│   │   │   ├── file.py
│   │   │   └── output.py
│   │   ├── 📁 schemas/             # Pydantic schemas
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── client.py
│   │   │   ├── file.py
│   │   │   └── ai.py
│   │   └── 📁 utils/               # Utilities
│   │       ├── __init__.py
│   │       ├── config.py           # Configuration management
│   │       ├── security.py         # Security utilities
│   │       └── helpers.py          # Helper functions
│   ├── 📁 data/                    # Data storage
│   │   ├── 📁 uploads/             # Uploaded files
│   │   ├── 📁 chroma_db/           # Vector database
│   │   └── 📁 test_data/           # Test files and data
│   ├── 📁 tests/                   # Test suite
│   │   ├── __init__.py
│   │   ├── test_auth.py
│   │   ├── test_clients.py
│   │   ├── test_ai.py
│   │   └── test_integration.py
│   ├── main.py                     # FastAPI application entry point
│   ├── requirements.txt            # Python dependencies
│   ├── .env                        # Environment variables
│   └── README.md                   # Backend documentation
├── 📁 frontend/                    # React frontend (existing)
├── 📁 docs/                        # Documentation
│   ├── API.md                      # API documentation
│   ├── SETUP.md                    # Setup instructions
│   └── ARCHITECTURE.md             # Architecture overview
├── 📁 scripts/                     # Utility scripts
│   ├── setup.sh                   # Environment setup
│   ├── test.sh                    # Run tests
│   └── deploy.sh                  # Deployment script
├── docker-compose.yml              # Docker configuration
├── .gitignore                      # Git ignore rules
└── README.md                       # Project overview
```

## 🔄 Migration Steps

### Step 1: Create New Structure
- Create clean directory structure
- Move core logic to appropriate modules
- Eliminate duplicate code

### Step 2: Consolidate Configuration
- Single .env file with all configurations
- Centralized config management
- Environment-specific settings

### Step 3: Refactor Code
- Extract common functionality
- Create proper abstractions
- Implement dependency injection

### Step 4: Update Imports
- Fix all import statements
- Use relative imports properly
- Remove circular dependencies

### Step 5: Migrate Data
- Move existing uploads and ChromaDB
- Preserve test data
- Update file paths

### Step 6: Update Tests
- Consolidate test files
- Create comprehensive test suite
- Add integration tests

## 🎯 Benefits of New Structure

✅ **Single Source of Truth** - No duplicate code
✅ **Clear Separation** - Each module has single responsibility
✅ **Easy Testing** - Modular structure enables unit testing
✅ **Scalable** - Easy to add new features
✅ **Maintainable** - Clear code organization
✅ **Production Ready** - Proper structure for deployment

## 🚀 Implementation Priority

1. **High Priority** - Core business logic (auth, database, AI)
2. **Medium Priority** - API routes and schemas
3. **Low Priority** - Tests and documentation
