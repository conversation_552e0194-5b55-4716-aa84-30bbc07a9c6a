# 🦙 Lemur AI - Production Ready Integration

**End-to-End Business Tool for IT Consulting Companies**

Lemur AI provides AI-powered meeting assistance, CRM integration, automated proposal generation, and maintains a centralized knowledge brain per company where all client interactions and project history are stored for contextual reference during content generation.

## 🎯 **For IT Consulting Companies**

### **Key Features:**
- **🏢 Client Management** - Hierarchical client/sub-client organization structure
- **🤖 AI Meeting Bots** - Automated meeting recording and transcription via Recall AI
- **📚 Centralized Knowledge Base** - Company brain that learns from all client interactions
- **✨ AI Content Generation** - Smart proposals, emails, summaries using company context
- **🔒 Data Isolation** - Secure per-client data separation and access control
- **📊 Production Dashboard** - Real-time overview of all business activities

## 🚀 **Quick Start**

### **1. Automated Setup**
```bash
# Clone and setup everything
<NAME_EMAIL>:AD1t12407/Lemur_AI.git
cd Lemur_AI
./setup_production.sh
```

### **2. Manual Setup**

#### **Backend Setup:**
```bash
cd backend_clean
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
cp .env.example .env
# Update .env with your API keys
python main.py
```

#### **Frontend Setup:**
```bash
cd frontend
npm install
cp .env.example .env
# Update .env with your configuration
npm run dev
```

### **3. Environment Configuration**

#### **Backend (.env):**
```env
# Database
DATABASE_URL=sqlite:///./lemur_ai.db

# OpenAI API
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
EMBEDDING_MODEL=text-embedding-ada-002

# Recall AI (Meeting Bots)
RECALL_API_KEY=your-recall-ai-api-key

# Security
JWT_SECRET_KEY=your-super-secure-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Google Calendar (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

#### **Frontend (.env):**
```env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_NAME=Lemur AI
VITE_ENABLE_DEBUG=true
```

## 🏗️ **Architecture**

### **Backend (FastAPI + SQLite + ChromaDB)**
```
backend_clean/
├── app/
│   ├── api/           # API routes (auth, clients, files, ai, bots)
│   ├── core/          # Core services (auth, database, AI, vector store)
│   ├── models/        # Database models
│   └── utils/         # Utilities and configuration
├── data/
│   ├── uploads/       # File storage
│   └── chroma_db/     # Vector database
└── main.py           # FastAPI application
```

### **Frontend (React + TypeScript + Tailwind)**
```
frontend/
├── src/
│   ├── components/    # Reusable UI components
│   ├── pages/         # Application pages
│   ├── services/      # API services
│   ├── stores/        # State management (Zustand)
│   └── types/         # TypeScript definitions
└── public/           # Static assets
```

## 📖 **API Documentation**

### **Authentication**
```bash
# Register user
POST /auth/register
{
  "email": "<EMAIL>",
  "password": "secure_password",
  "name": "John Doe"
}

# Login
POST /auth/login
{
  "email": "<EMAIL>", 
  "password": "secure_password"
}
```

### **Client Management**
```bash
# Create client organization
POST /clients/
{
  "name": "Acme Corporation",
  "description": "Main client organization"
}

# Create sub-client
POST /clients/{client_id}/sub-clients
{
  "name": "AI Project",
  "description": "Machine learning initiative"
}
```

### **Knowledge Base**
```bash
# Upload file
POST /files/upload
FormData: file, client_id, sub_client_id

# Search knowledge
POST /files/search
{
  "query": "project requirements",
  "client_id": "uuid",
  "n_results": 5
}
```

### **AI Content Generation**
```bash
# Generate email
POST /ai/email
{
  "prompt": "Follow up on project meeting",
  "client_id": "uuid",
  "recipient_name": "Client Name",
  "sender_name": "Your Name"
}

# Generate proposal
POST /ai/generate
{
  "prompt": "Create project proposal",
  "content_type": "proposal",
  "client_id": "uuid"
}
```

### **Meeting Bots**
```bash
# Create bot
POST /create-bot
{
  "meeting_url": "https://meet.google.com/abc-defg-hij",
  "bot_name": "Meeting Recorder"
}

# Get bot status
GET /bot/{bot_id}/status

# Get recordings
GET /bot/{bot_id}/download-urls
```

## 🎯 **User Workflows**

### **For IT Consulting Companies:**

#### **1. Initial Setup**
1. Register company account
2. Create client organizations
3. Set up sub-clients for different projects
4. Upload existing project documents

#### **2. Meeting Management**
1. Schedule client meetings
2. Create Recall AI bots for automatic recording
3. Bots join meetings and record/transcribe
4. AI processes transcripts into actionable insights

#### **3. Knowledge Building**
1. Upload contracts, proposals, meeting notes
2. System builds searchable knowledge base per client
3. AI learns company communication style and preferences
4. Context accumulates for better future generations

#### **4. Content Generation**
1. Generate follow-up emails using meeting context
2. Create proposals based on client history
3. Draft project summaries with relevant background
4. All content uses company-specific knowledge

#### **5. Client Collaboration**
1. Sub-clients can access shared knowledge within organization
2. Parent client has visibility across all sub-clients
3. Secure data isolation between different client organizations

## 🔧 **Development**

### **Running Tests**
```bash
# Backend tests
cd backend_clean
source venv/bin/activate
python test_complete_workflow.py
python test_real_bots.py

# Frontend tests
cd frontend
npm test
```

### **API Testing**
- **Swagger UI**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Debug Endpoints**: http://localhost:8000/debug/users

### **Production Dashboard**
- **URL**: http://localhost:5173/production
- **Features**: Real-time client overview, bot management, knowledge search

## 🚀 **Deployment**

### **Backend Deployment**
```bash
# Using Docker
docker build -t lemur-ai-backend .
docker run -p 8000:8000 lemur-ai-backend

# Using systemd service
sudo cp lemur-ai.service /etc/systemd/system/
sudo systemctl enable lemur-ai
sudo systemctl start lemur-ai
```

### **Frontend Deployment**
```bash
# Build for production
npm run build

# Deploy to Vercel/Netlify
npm run deploy
```

## 🔒 **Security**

- **JWT Authentication** - Secure token-based auth
- **Data Isolation** - Per-client data separation
- **API Rate Limiting** - Prevent abuse
- **Input Validation** - Sanitize all inputs
- **CORS Configuration** - Secure cross-origin requests

## 📊 **Monitoring**

- **Health Checks** - `/health` endpoint
- **Debug Endpoints** - Development monitoring
- **Error Logging** - Comprehensive error tracking
- **Performance Metrics** - API response times

## 🤝 **Support**

- **Documentation**: Full API docs at `/docs`
- **Examples**: Test scripts in `backend_clean/`
- **Issues**: GitHub issues for bug reports
- **Features**: Feature requests welcome

## 📝 **License**

MIT License - see LICENSE file for details.

---

**Built for IT Consulting Companies** 🏢  
**Powered by AI** 🤖  
**Production Ready** 🚀
