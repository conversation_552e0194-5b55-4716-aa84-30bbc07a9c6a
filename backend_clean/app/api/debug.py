"""
Debug API routes
"""

from fastapi import APIRouter, HTTPException
from app.core.database import db_manager, get_supabase_client
from app.core.auth import DEMO_USERS

router = APIRouter()


@router.get("/users")
async def debug_users():
    """Debug: Get all users in database"""
    try:
        supabase = get_supabase_client()
        result = supabase.table("users").select("*").execute()
        
        return {
            "total_users": len(result.data),
            "users": result.data,
            "demo_users": list(DEMO_USERS.keys())
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching users: {str(e)}")


@router.get("/test")
async def debug_test():
    """Debug: Test database connection and basic functionality"""
    try:
        supabase = get_supabase_client()
        
        # Test each table
        tables_status = {}
        tables = ["users", "clients", "sub_clients", "files", "outputs"]
        
        for table in tables:
            try:
                result = supabase.table(table).select("id").limit(1).execute()
                tables_status[table] = {
                    "accessible": True,
                    "count": len(result.data)
                }
            except Exception as e:
                tables_status[table] = {
                    "accessible": False,
                    "error": str(e)
                }
        
        return {
            "database_connection": "OK",
            "tables": tables_status,
            "demo_users_available": len(DEMO_USERS)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Debug test failed: {str(e)}")


@router.get("/google-tokens")
async def debug_google_tokens():
    """Debug: Check Google OAuth tokens status"""
    # This would check stored Google tokens in the database
    # For now, return placeholder
    return {
        "message": "Google tokens debug endpoint",
        "status": "not_implemented",
        "note": "This would show stored Google OAuth tokens for debugging"
    }


@router.post("/connect-user/{user_id}")
async def manually_connect_user(user_id: str):
    """Debug: Manually connect user (for testing)"""
    try:
        user = await db_manager.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return {
            "message": f"User {user_id} manually connected",
            "user": {
                "id": user["id"],
                "email": user["email"],
                "name": user["name"]
            },
            "status": "connected"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error connecting user: {str(e)}")
