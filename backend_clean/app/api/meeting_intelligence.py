"""
Meeting Intelligence API Routes
End-to-end meeting processing with AI
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, HttpUrl
from datetime import datetime
from app.core.auth import get_current_user
from app.core.meeting_intelligence import meeting_intelligence
from app.core.database import db_manager

router = APIRouter()


# Request/Response Models
class StartMeetingRecordingRequest(BaseModel):
    """Request to start meeting recording"""
    meeting_url: HttpUrl
    meeting_title: str
    client_id: str
    sub_client_id: Optional[str] = None
    attendees: Optional[List[str]] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "meeting_url": "https://meet.google.com/abc-defg-hij",
                "meeting_title": "Tech Stand Up",
                "client_id": "client-123",
                "sub_client_id": "subclient-456",
                "attendees": ["<EMAIL>", "<EMAIL>"]
            }
        }


class MeetingRecordingResponse(BaseModel):
    """Response for meeting recording start"""
    success: bool
    meeting_id: str
    bot_id: Optional[str] = None
    status: str
    message: str
    started_at: datetime


class MeetingStatusResponse(BaseModel):
    """Response for meeting status"""
    meeting_id: str
    status: str
    bot_id: Optional[str] = None
    meeting_title: str
    client_id: str
    processed: bool
    started_at: datetime
    processed_at: Optional[datetime] = None


class MeetingResultsResponse(BaseModel):
    """Response for meeting results"""
    meeting_id: str
    summary: Optional[str] = None
    action_items: Optional[str] = None
    follow_up_email: Optional[str] = None
    transcript_available: bool = False
    video_url: Optional[str] = None
    audio_url: Optional[str] = None


@router.post("/start-recording", response_model=MeetingRecordingResponse)
async def start_meeting_recording(
    request: StartMeetingRecordingRequest,
    current_user_id: str = Depends(get_current_user)
):
    """
    Start intelligent meeting recording
    
    This endpoint:
    1. Creates a Recall AI bot to join the meeting
    2. Starts monitoring for completion
    3. Will automatically process with AI when meeting ends
    4. Generates summary, action items, and follow-up email
    5. Uses client context from uploaded documents
    """
    try:
        # Verify user owns the client
        clients = await db_manager.get_clients_by_user(current_user_id)
        if not any(client["id"] == request.client_id for client in clients):
            raise HTTPException(
                status_code=403,
                detail="You don't have permission to record meetings for this client"
            )
        
        # Generate unique meeting ID
        import uuid
        meeting_id = str(uuid.uuid4())
        
        # Start recording with intelligence service
        result = await meeting_intelligence.start_meeting_recording(
            meeting_url=str(request.meeting_url),
            meeting_id=meeting_id,
            client_id=request.client_id,
            sub_client_id=request.sub_client_id,
            user_id=current_user_id,
            attendees=request.attendees,
            meeting_title=request.meeting_title
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to start meeting recording: {result.get('error')}"
            )
        
        return MeetingRecordingResponse(
            success=True,
            meeting_id=meeting_id,
            bot_id=result.get("bot_id"),
            status=result.get("status", "starting"),
            message=result.get("message", "Meeting recording started"),
            started_at=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/status/{meeting_id}", response_model=MeetingStatusResponse)
async def get_meeting_status(
    meeting_id: str,
    current_user_id: str = Depends(get_current_user)
):
    """Get current status of a meeting recording"""
    try:
        meeting_data = meeting_intelligence.get_meeting_status(meeting_id)
        
        if not meeting_data:
            raise HTTPException(
                status_code=404,
                detail="Meeting not found"
            )
        
        # Verify user owns this meeting
        if meeting_data.get("user_id") != current_user_id:
            raise HTTPException(
                status_code=403,
                detail="You don't have permission to access this meeting"
            )
        
        return MeetingStatusResponse(
            meeting_id=meeting_id,
            status=meeting_data.get("status", "unknown"),
            bot_id=meeting_data.get("bot_id"),
            meeting_title=meeting_data.get("meeting_title", "Unknown"),
            client_id=meeting_data.get("client_id", ""),
            processed=meeting_data.get("processed", False),
            started_at=meeting_data.get("started_at", datetime.now()),
            processed_at=meeting_data.get("processed_at")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/results/{meeting_id}", response_model=MeetingResultsResponse)
async def get_meeting_results(
    meeting_id: str,
    current_user_id: str = Depends(get_current_user)
):
    """
    Get AI-generated results for a completed meeting
    
    Returns summary, action items, follow-up email, and media URLs
    """
    try:
        meeting_data = meeting_intelligence.get_meeting_status(meeting_id)
        
        if not meeting_data:
            raise HTTPException(
                status_code=404,
                detail="Meeting not found"
            )
        
        # Verify user owns this meeting
        if meeting_data.get("user_id") != current_user_id:
            raise HTTPException(
                status_code=403,
                detail="You don't have permission to access this meeting"
            )
        
        if not meeting_data.get("processed", False):
            raise HTTPException(
                status_code=202,
                detail="Meeting is still being processed. Please check back later."
            )
        
        # Get AI outputs from database
        client_id = meeting_data.get("client_id")
        outputs = await db_manager.get_outputs_by_client(client_id)
        
        # Filter outputs for this meeting
        meeting_outputs = [
            output for output in outputs 
            if output.get("metadata", {}).get("meeting_id") == meeting_id
        ]
        
        # Extract different content types
        summary = None
        action_items = None
        follow_up_email = None
        video_url = None
        audio_url = None
        
        for output in meeting_outputs:
            content_type = output.get("content_type")
            content = output.get("content")
            metadata = output.get("metadata", {})
            
            if content_type == "summary":
                summary = content
            elif content_type == "action_items":
                action_items = content
            elif content_type == "follow_up_email":
                follow_up_email = content
            
            # Get media URLs from metadata
            if not video_url:
                video_url = metadata.get("video_url")
            if not audio_url:
                audio_url = metadata.get("audio_url")
        
        return MeetingResultsResponse(
            meeting_id=meeting_id,
            summary=summary,
            action_items=action_items,
            follow_up_email=follow_up_email,
            transcript_available=True,  # We always have transcript if processed
            video_url=video_url,
            audio_url=audio_url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/active", response_model=List[MeetingStatusResponse])
async def list_active_meetings(current_user_id: str = Depends(get_current_user)):
    """List all active meetings for the current user"""
    try:
        all_meetings = meeting_intelligence.list_active_meetings()
        
        # Filter meetings for current user
        user_meetings = [
            meeting for meeting in all_meetings 
            if meeting.get("user_id") == current_user_id
        ]
        
        return [
            MeetingStatusResponse(
                meeting_id=meeting.get("meeting_id", ""),
                status=meeting.get("status", "unknown"),
                bot_id=meeting.get("bot_id"),
                meeting_title=meeting.get("meeting_title", "Unknown"),
                client_id=meeting.get("client_id", ""),
                processed=meeting.get("processed", False),
                started_at=meeting.get("started_at", datetime.now()),
                processed_at=meeting.get("processed_at")
            )
            for meeting in user_meetings
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/process-manual/{meeting_id}")
async def manually_process_meeting(
    meeting_id: str,
    current_user_id: str = Depends(get_current_user)
):
    """
    Manually trigger AI processing for a completed meeting
    
    Useful if automatic processing failed or needs to be re-run
    """
    try:
        meeting_data = meeting_intelligence.get_meeting_status(meeting_id)
        
        if not meeting_data:
            raise HTTPException(
                status_code=404,
                detail="Meeting not found"
            )
        
        # Verify user owns this meeting
        if meeting_data.get("user_id") != current_user_id:
            raise HTTPException(
                status_code=403,
                detail="You don't have permission to process this meeting"
            )
        
        # Trigger manual processing
        import asyncio
        asyncio.create_task(
            meeting_intelligence._process_completed_meeting(meeting_id)
        )
        
        return {
            "success": True,
            "message": "Manual processing started for meeting",
            "meeting_id": meeting_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
