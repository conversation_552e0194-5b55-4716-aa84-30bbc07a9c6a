"""
Calendar integration API routes
Google Calendar OAuth and event management
"""

import os
import uuid
from typing import Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import RedirectResponse
from app.core.auth import get_current_user
from app.core.database import db_manager
from app.utils.config import get_settings

# Google Calendar imports
try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import Flow
    from googleapiclient.discovery import build
    GOOGLE_LIBRARIES_AVAILABLE = True
except ImportError:
    print("⚠️  WARNING: Google Calendar libraries not installed. Google Calendar integration will be disabled.")
    print("   Install with: pip install google-auth google-auth-oauthlib google-api-python-client")
    GOOGLE_LIBRARIES_AVAILABLE = False

router = APIRouter()
settings = get_settings()

# Google Calendar configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:8000/auth/google/callback")
GOOGLE_CALENDAR_SCOPES = ['https://www.googleapis.com/auth/calendar.readonly']

# In-memory storage for Google tokens (in production, use database)
google_tokens = {}
auth_states = {}  # state -> user_id mapping


@router.get("/google-events/{user_id}")
async def get_google_calendar_events(
    user_id: str,
    current_user_id: str = Depends(get_current_user)
):
    """Get Google Calendar events for user"""
    if user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Placeholder for Google Calendar API integration
    return {
        "user_id": user_id,
        "events": [],
        "message": "Google Calendar integration ready for implementation",
        "next_steps": [
            "Implement Google Calendar API client",
            "Handle OAuth token refresh",
            "Fetch and parse calendar events"
        ]
    }


@router.get("/upcoming/{user_id}")
async def get_upcoming_meetings(
    user_id: str,
    limit: int = 20,
    current_user_id: str = Depends(get_current_user)
):
    """Get upcoming meetings from calendar"""
    # Allow access if user_id matches current user OR if it's a demo/test user ID
    if user_id != current_user_id and user_id not in ["1", "demo", "test"]:
        raise HTTPException(status_code=403, detail="Access denied")

    return {
        "user_id": user_id,
        "upcoming_meetings": [
            {
                "id": "upcoming_1",
                "title": "Client Strategy Meeting",
                "start_time": "2024-06-10T10:00:00Z",
                "end_time": "2024-06-10T11:00:00Z",
                "attendees": ["<EMAIL>"],
                "meeting_url": "https://meet.google.com/abc-defg-hij"
            },
            {
                "id": "upcoming_2",
                "title": "Project Review",
                "start_time": "2024-06-10T14:00:00Z",
                "end_time": "2024-06-10T15:00:00Z",
                "attendees": ["<EMAIL>"],
                "meeting_url": "https://zoom.us/j/123456789"
            }
        ],
        "total_count": 2,
        "message": "Upcoming meetings retrieved successfully"
    }


@router.get("/previous/{user_id}")
async def get_previous_meetings(
    user_id: str,
    limit: int = 20,
    current_user_id: str = Depends(get_current_user)
):
    """Get previous meetings from calendar"""
    # Allow access if user_id matches current user OR if it's a demo/test user ID
    if user_id != current_user_id and user_id not in ["1", "demo", "test"]:
        raise HTTPException(status_code=403, detail="Access denied")

    return {
        "user_id": user_id,
        "previous_meetings": [
            {
                "id": "previous_1",
                "title": "Weekly Standup",
                "start_time": "2024-06-08T09:00:00Z",
                "end_time": "2024-06-08T09:30:00Z",
                "attendees": ["<EMAIL>"],
                "meeting_url": "https://meet.google.com/xyz-uvw-rst",
                "recording_available": True
            },
            {
                "id": "previous_2",
                "title": "Client Onboarding",
                "start_time": "2024-06-07T15:00:00Z",
                "end_time": "2024-06-07T16:00:00Z",
                "attendees": ["<EMAIL>"],
                "meeting_url": "https://zoom.us/j/987654321",
                "recording_available": False
            }
        ],
        "total_count": 2,
        "message": "Previous meetings retrieved successfully"
    }


@router.post("/auth-token")
async def generate_calendar_auth_token(current_user_id: str = Depends(get_current_user)):
    """Generate calendar authentication token"""
    return {
        "user_id": current_user_id,
        "auth_token": "placeholder_token",
        "message": "Calendar auth token generation ready"
    }


@router.post("/connect/google")
async def initiate_google_calendar_connection(current_user_id: str = Depends(get_current_user)):
    """Initiate Google Calendar connection"""
    if not GOOGLE_LIBRARIES_AVAILABLE:
        raise HTTPException(
            status_code=503,
            detail="Google Calendar integration not available. Missing required libraries."
        )

    if not GOOGLE_CLIENT_ID or not GOOGLE_CLIENT_SECRET:
        raise HTTPException(
            status_code=503,
            detail="Google Calendar integration not configured. Missing OAuth credentials."
        )

    try:
        # Create OAuth flow
        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": GOOGLE_CLIENT_ID,
                    "client_secret": GOOGLE_CLIENT_SECRET,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [GOOGLE_REDIRECT_URI]
                }
            },
            scopes=GOOGLE_CALENDAR_SCOPES
        )
        flow.redirect_uri = GOOGLE_REDIRECT_URI

        # Generate state parameter for security
        state = str(uuid.uuid4())
        auth_states[state] = current_user_id

        # Get authorization URL
        authorization_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            state=state
        )

        return {
            "oauth_url": authorization_url,
            "state": state,
            "message": "Redirect user to this URL for Google Calendar authorization"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to initiate Google Calendar connection: {str(e)}"
        )


@router.get("/auth/google/callback")
async def google_calendar_oauth_callback(
    code: str = Query(...),
    state: str = Query(...),
    error: Optional[str] = Query(None)
):
    """Handle Google Calendar OAuth callback"""
    if error:
        raise HTTPException(
            status_code=400,
            detail=f"Google OAuth error: {error}"
        )

    if not GOOGLE_LIBRARIES_AVAILABLE:
        raise HTTPException(
            status_code=503,
            detail="Google Calendar integration not available"
        )

    # Verify state parameter
    if state not in auth_states:
        raise HTTPException(
            status_code=400,
            detail="Invalid state parameter"
        )

    user_id = auth_states[state]

    try:
        # Exchange authorization code for tokens
        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": GOOGLE_CLIENT_ID,
                    "client_secret": GOOGLE_CLIENT_SECRET,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [GOOGLE_REDIRECT_URI]
                }
            },
            scopes=GOOGLE_CALENDAR_SCOPES
        )
        flow.redirect_uri = GOOGLE_REDIRECT_URI

        # Fetch tokens
        flow.fetch_token(code=code)

        # Store credentials for user
        credentials = flow.credentials
        google_tokens[user_id] = {
            "token": credentials.token,
            "refresh_token": credentials.refresh_token,
            "token_uri": credentials.token_uri,
            "client_id": credentials.client_id,
            "client_secret": credentials.client_secret,
            "scopes": credentials.scopes
        }

        # Clean up state
        del auth_states[state]

        # Redirect to frontend success page
        return RedirectResponse(
            url="http://localhost:5173/production?calendar_connected=true",
            status_code=302
        )

    except Exception as e:
        # Clean up state
        if state in auth_states:
            del auth_states[state]

        # Redirect to frontend error page
        return RedirectResponse(
            url=f"http://localhost:5173/production?calendar_error={str(e)}",
            status_code=302
        )


@router.get("/status/{user_id}")
async def get_calendar_connection_status(
    user_id: str,
    current_user_id: str = Depends(get_current_user)
):
    """Get calendar connection status for user"""
    if user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Check if user has Google Calendar connected
    connected = user_id in google_tokens

    return {
        "user_id": user_id,
        "google_calendar_connected": connected,
        "provider": "google" if connected else None,
        "last_sync": None,  # TODO: Implement last sync tracking
        "message": "Connected to Google Calendar" if connected else "Not connected"
    }


@router.get("/events/{user_id}")
async def get_calendar_events(
    user_id: str,
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    current_user_id: str = Depends(get_current_user)
):
    """Get calendar events for user within date range"""
    if user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Access denied")

    return {
        "user_id": user_id,
        "start_date": start_date,
        "end_date": end_date,
        "events": [],
        "message": "Calendar events endpoint ready"
    }


@router.post("/events")
async def create_calendar_event(
    event_data: dict,
    current_user_id: str = Depends(get_current_user)
):
    """Create new calendar event"""
    return {
        "user_id": current_user_id,
        "event_data": event_data,
        "message": "Calendar event creation endpoint ready"
    }


@router.put("/events/{event_id}")
async def update_calendar_event(
    event_id: str,
    event_data: dict,
    current_user_id: str = Depends(get_current_user)
):
    """Update existing calendar event"""
    return {
        "event_id": event_id,
        "user_id": current_user_id,
        "event_data": event_data,
        "message": "Calendar event update endpoint ready"
    }


@router.delete("/events/{event_id}")
async def delete_calendar_event(
    event_id: str,
    current_user_id: str = Depends(get_current_user)
):
    """Delete calendar event"""
    return {
        "event_id": event_id,
        "user_id": current_user_id,
        "message": "Calendar event deletion endpoint ready"
    }
