"""
Recall AI service integration
Real implementation for meeting bot management
"""

import logging
import requests
from typing import Dict, Any, List
from app.utils.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class RecallAIBot:
    """
    Real Recall AI bot implementation
    Handles meeting recording bot creation and management
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://us-west-2.recall.ai/api/v1"
        self.headers = {
            "Authorization": f"Token {api_key}",
            "Content-Type": "application/json"
        }
    
    def create_bot(self, meeting_url: str, bot_name: str = "Lemur AI Bot") -> Dict[str, Any]:
        """Create a new Recall AI bot for meeting recording"""
        try:
            # Updated payload format based on Recall AI API v1
            payload = {
                "meeting_url": meeting_url,
                "bot_name": bot_name,
                "transcription_options": {
                    "provider": "assembly_ai"
                },
                "recording_mode": "speaker_view",
                "recording_mode_options": {
                    "participant_video_when_screenshare": True
                },
                "real_time_transcription": {
                    "destination_url": None,
                    "partial_results": True
                },
                "automatic_leave": {
                    "waiting_room_timeout": 1200,
                    "noone_joined_timeout": 1200
                }
            }
            
            response = requests.post(
                f"{self.base_url}/bot",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                bot_data = response.json()
                logger.info(f"✅ Recall AI bot created successfully: {bot_data.get('id')}")
                return {
                    "success": True,
                    "bot_id": bot_data.get("id"),
                    "status": bot_data.get("status_changes", [{}])[-1].get("code", "unknown"),
                    "meeting_url": bot_data.get("meeting_url"),
                    "bot_name": bot_data.get("bot_name"),
                    "created_at": bot_data.get("created_at"),
                    "data": bot_data
                }
            else:
                error_detail = response.text
                try:
                    error_json = response.json()
                    error_detail = error_json.get("detail", error_json)
                except:
                    pass

                logger.error(f"❌ Recall AI bot creation failed: {response.status_code}")
                logger.error(f"   Request payload: {payload}")
                logger.error(f"   Response: {error_detail}")

                return {
                    "success": False,
                    "error": f"Bot creation failed: {response.status_code}",
                    "detail": error_detail,
                    "request_payload": payload
                }
                
        except Exception as e:
            logger.error(f"❌ Error creating Recall AI bot: {e}")
            return {
                "success": False,
                "error": "Bot creation error",
                "detail": str(e)
            }
    
    def get_bot_status(self, bot_id: str) -> Dict[str, Any]:
        """Get the current status of a Recall AI bot"""
        try:
            response = requests.get(
                f"{self.base_url}/bot/{bot_id}",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                bot_data = response.json()
                status_changes = bot_data.get("status_changes", [])
                current_status = status_changes[-1].get("code", "unknown") if status_changes else "unknown"
                
                return {
                    "success": True,
                    "bot_id": bot_id,
                    "status": current_status,
                    "meeting_url": bot_data.get("meeting_url"),
                    "bot_name": bot_data.get("bot_name"),
                    "created_at": bot_data.get("created_at"),
                    "status_changes": status_changes,
                    "data": bot_data
                }
            else:
                logger.error(f"❌ Failed to get bot status: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Failed to get bot status: {response.status_code}",
                    "detail": response.text
                }
                
        except Exception as e:
            logger.error(f"❌ Error getting bot status: {e}")
            return {
                "success": False,
                "error": "Bot status error",
                "detail": str(e)
            }
    
    def get_download_urls(self, bot_id: str) -> Dict[str, Any]:
        """Get download URLs for bot recordings"""
        try:
            response = requests.get(
                f"{self.base_url}/bot/{bot_id}",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                bot_data = response.json()
                
                return {
                    "success": True,
                    "bot_id": bot_id,
                    "video_url": bot_data.get("video_url"),
                    "audio_url": bot_data.get("audio_url"),
                    "transcript_url": bot_data.get("transcript_url"),
                    "chat_messages_url": bot_data.get("chat_messages_url"),
                    "status": bot_data.get("status_changes", [{}])[-1].get("code", "unknown"),
                    "data": bot_data
                }
            else:
                logger.error(f"❌ Failed to get download URLs: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Failed to get download URLs: {response.status_code}",
                    "detail": response.text
                }
                
        except Exception as e:
            logger.error(f"❌ Error getting download URLs: {e}")
            return {
                "success": False,
                "error": "Download URLs error",
                "detail": str(e)
            }
    
    def delete_bot(self, bot_id: str) -> Dict[str, Any]:
        """Delete/stop a Recall AI bot"""
        try:
            response = requests.delete(
                f"{self.base_url}/bot/{bot_id}",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 204:
                logger.info(f"✅ Recall AI bot deleted successfully: {bot_id}")
                return {
                    "success": True,
                    "bot_id": bot_id,
                    "status": "deleted",
                    "message": "Bot deleted successfully"
                }
            else:
                logger.error(f"❌ Failed to delete bot: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Failed to delete bot: {response.status_code}",
                    "detail": response.text
                }
                
        except Exception as e:
            logger.error(f"❌ Error deleting bot: {e}")
            return {
                "success": False,
                "error": "Bot deletion error",
                "detail": str(e)
            }
    
    def list_bots(self) -> Dict[str, Any]:
        """List all bots for the API key"""
        try:
            response = requests.get(
                f"{self.base_url}/bot",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                bots_data = response.json()
                return {
                    "success": True,
                    "bots": bots_data.get("results", []),
                    "total_count": bots_data.get("count", 0),
                    "data": bots_data
                }
            else:
                logger.error(f"❌ Failed to list bots: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Failed to list bots: {response.status_code}",
                    "detail": response.text
                }
                
        except Exception as e:
            logger.error(f"❌ Error listing bots: {e}")
            return {
                "success": False,
                "error": "Bot listing error",
                "detail": str(e)
            }


# Global Recall AI service instance
recall_service = RecallAIBot(settings.recall_api_key)


# In-memory storage for user-bot mapping (in production, use database)
user_bots: Dict[str, List[str]] = {}  # user_id -> list of bot_ids


def get_user_bots(user_id: str) -> List[str]:
    """Get bot IDs for a specific user"""
    return user_bots.get(user_id, [])


def add_user_bot(user_id: str, bot_id: str):
    """Add a bot ID to a user's bot list"""
    if user_id not in user_bots:
        user_bots[user_id] = []
    user_bots[user_id].append(bot_id)


def remove_user_bot(user_id: str, bot_id: str):
    """Remove a bot ID from a user's bot list"""
    if user_id in user_bots and bot_id in user_bots[user_id]:
        user_bots[user_id].remove(bot_id)


def cleanup_old_bots(user_id: str) -> Dict[str, Any]:
    """Cleanup old/inactive bots for a user"""
    try:
        user_bot_ids = get_user_bots(user_id)
        cleaned_up = 0
        
        for bot_id in user_bot_ids.copy():
            status_result = recall_service.get_bot_status(bot_id)
            if status_result["success"]:
                status = status_result["status"]
                # Clean up bots that are done, failed, or error
                if status in ["done", "failed", "error", "fatal"]:
                    remove_user_bot(user_id, bot_id)
                    cleaned_up += 1
                    logger.info(f"🧹 Cleaned up bot {bot_id} with status {status}")
        
        return {
            "success": True,
            "cleaned_up": cleaned_up,
            "remaining_bots": len(get_user_bots(user_id))
        }
        
    except Exception as e:
        logger.error(f"❌ Error cleaning up bots: {e}")
        return {
            "success": False,
            "error": "Cleanup error",
            "detail": str(e)
        }
