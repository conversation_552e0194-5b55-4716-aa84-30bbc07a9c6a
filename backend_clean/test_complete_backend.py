#!/usr/bin/env python3
"""
Comprehensive test suite for clean Lemur AI backend
Tests all functionality end-to-end
"""

import requests
import json
import time
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "demo1234"

class LemurAITester:
    def __init__(self):
        self.token = None
        self.user_id = None
        self.client_id = None
        self.sub_client_id = None
        self.file_id = None
        
    def test_health(self):
        """Test API health"""
        print("🔍 Testing API Health...")
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API is healthy")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    
    def test_authentication(self):
        """Test user authentication"""
        print("🔐 Testing Authentication...")
        
        # Test login
        login_data = {
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }
        
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            self.token = data["access_token"]
            self.user_id = data["user"]["id"]
            print(f"✅ Login successful - User: {data['user']['name']}")
            return True
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
    
    def get_headers(self):
        """Get authorization headers"""
        return {"Authorization": f"Bearer {self.token}"}
    
    def test_client_management(self):
        """Test client creation and management"""
        print("🏢 Testing Client Management...")
        
        # Create client
        client_data = {
            "name": "Test Company - Centralized Brain",
            "description": "Testing the centralized knowledge base concept"
        }
        
        response = requests.post(
            f"{BASE_URL}/clients/",
            json=client_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            data = response.json()
            self.client_id = data["id"]
            print(f"✅ Client created: {data['name']} (ID: {self.client_id})")
        else:
            print(f"❌ Client creation failed: {response.status_code} - {response.text}")
            return False
        
        # Create sub-client
        sub_client_data = {
            "name": "AI Development Project",
            "description": "Machine learning and AI development",
            "contact_email": "<EMAIL>",
            "contact_name": "Sarah Johnson"
        }
        
        response = requests.post(
            f"{BASE_URL}/clients/{self.client_id}/sub-clients",
            json=sub_client_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            data = response.json()
            self.sub_client_id = data["id"]
            print(f"✅ Sub-client created: {data['name']} (ID: {self.sub_client_id})")
            return True
        else:
            print(f"❌ Sub-client creation failed: {response.status_code} - {response.text}")
            return False
    
    def test_file_upload(self):
        """Test file upload and processing"""
        print("📄 Testing File Upload and Processing...")
        
        # Create test file
        test_content = """
        LEMUR AI TEST DOCUMENT
        
        This is a comprehensive test document for the Lemur AI platform.
        
        EXECUTIVE SUMMARY:
        The Lemur platform successfully processes documents and generates AI-powered insights
        using the centralized brain concept where all company knowledge is stored and accessible.
        
        KEY FEATURES:
        - Document processing and text extraction
        - Vector embeddings for semantic search
        - AI-powered content generation using company context
        - Multi-tenant data isolation
        
        ACTION ITEMS:
        1. Verify file upload functionality
        2. Test text extraction and embedding
        3. Validate AI content generation
        4. Check knowledge base search
        
        CONTACT INFORMATION:
        Project Manager: John Doe
        Email: <EMAIL>
        Phone: (*************
        
        This document demonstrates the centralized brain concept where all client
        information is stored and can be retrieved for contextual AI generation.
        """
        
        # Save test file
        test_file_path = Path("test_document.txt")
        test_file_path.write_text(test_content)
        
        try:
            # Upload file
            with open(test_file_path, 'rb') as f:
                files = {'file': ('test_document.txt', f, 'text/plain')}
                data = {
                    'client_id': self.client_id,
                    'sub_client_id': self.sub_client_id
                }
                
                response = requests.post(
                    f"{BASE_URL}/files/upload",
                    files=files,
                    data=data,
                    headers=self.get_headers()
                )
            
            if response.status_code == 200:
                data = response.json()
                self.file_id = data["id"]
                print(f"✅ File uploaded and processed: {data['original_filename']}")
                print(f"   Chunks stored: {data['chunks_stored']}")
                print(f"   Text preview: {data['extracted_text'][:100]}...")
                return True
            else:
                print(f"❌ File upload failed: {response.status_code} - {response.text}")
                return False
                
        finally:
            # Cleanup test file
            if test_file_path.exists():
                test_file_path.unlink()
    
    def test_knowledge_search(self):
        """Test knowledge base search"""
        print("🔍 Testing Knowledge Base Search...")
        
        search_data = {
            "query": "executive summary project manager contact information",
            "client_id": self.client_id,
            "sub_client_id": self.sub_client_id,
            "n_results": 3
        }
        
        response = requests.post(
            f"{BASE_URL}/files/search",
            json=search_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Knowledge search successful: {data['total_results']} results")
            if data['results']:
                print(f"   Top result: {data['results'][0]['text'][:100]}...")
            return True
        else:
            print(f"❌ Knowledge search failed: {response.status_code} - {response.text}")
            return False
    
    def test_ai_generation(self):
        """Test AI content generation"""
        print("🤖 Testing AI Content Generation...")
        
        # Test email generation
        email_data = {
            "prompt": "Write a follow-up email to the client about our AI development project progress",
            "content_type": "email",
            "client_id": self.client_id,
            "sub_client_id": self.sub_client_id,
            "recipient_name": "Client Team",
            "sender_name": "Project Manager"
        }
        
        response = requests.post(
            f"{BASE_URL}/ai/generate",
            json=email_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ AI email generated successfully")
            print(f"   Content preview: {data['content'][:150]}...")
            print(f"   Context used: {len(data['context_used'])} characters")
        else:
            print(f"❌ AI email generation failed: {response.status_code} - {response.text}")
            return False
        
        # Test summary generation
        summary_data = {
            "prompt": "Create an executive summary of our project status and key achievements",
            "content_type": "summary",
            "client_id": self.client_id,
            "sub_client_id": self.sub_client_id
        }
        
        response = requests.post(
            f"{BASE_URL}/ai/generate",
            json=summary_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ AI summary generated successfully")
            print(f"   Content preview: {data['content'][:150]}...")
            return True
        else:
            print(f"❌ AI summary generation failed: {response.status_code} - {response.text}")
            return False
    
    def run_all_tests(self):
        """Run complete test suite"""
        print("🧪 Lemur AI - Complete Backend Test Suite")
        print("=" * 60)
        
        tests = [
            ("API Health", self.test_health),
            ("Authentication", self.test_authentication),
            ("Client Management", self.test_client_management),
            ("File Upload & Processing", self.test_file_upload),
            ("Knowledge Search", self.test_knowledge_search),
            ("AI Content Generation", self.test_ai_generation)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 Running: {test_name}")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} ERROR: {e}")
        
        print("\n" + "=" * 60)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Lemur AI backend is fully functional!")
            print("\n🧠 Centralized Brain Features Verified:")
            print("   ✅ Document processing and knowledge storage")
            print("   ✅ Semantic search across company knowledge")
            print("   ✅ AI content generation with context")
            print("   ✅ Multi-tenant data isolation")
        else:
            print(f"⚠️  {total - passed} tests failed. Please check the issues above.")
        
        return passed == total

if __name__ == "__main__":
    tester = LemurAITester()
    success = tester.run_all_tests()
    exit(0 if success else 1)
