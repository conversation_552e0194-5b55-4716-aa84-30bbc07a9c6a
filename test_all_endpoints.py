#!/usr/bin/env python3
"""
Comprehensive test of all backend endpoints
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_all_endpoints():
    """Test all backend endpoints systematically"""
    print("🧪 Testing All Backend Endpoints")
    print("=" * 60)
    
    results = {
        "passed": 0,
        "failed": 0,
        "endpoints": {}
    }
    
    # Step 1: Health Check
    print("🔍 Testing Health Check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            results["passed"] += 1
            results["endpoints"]["/health"] = "✅ PASS"
        else:
            print(f"❌ Health check failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/health"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Health check error: {e}")
        results["failed"] += 1
        results["endpoints"]["/health"] = f"❌ ERROR ({e})"
    
    # Step 2: Authentication
    print("\n🔐 Testing Authentication...")
    token = None
    user_id = None
    
    # Login
    try:
        login_data = {"email": "<EMAIL>", "password": "demo1234"}
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data["access_token"]
            user_id = data["user"]["id"]
            print("✅ Login successful")
            results["passed"] += 1
            results["endpoints"]["/auth/login"] = "✅ PASS"
        else:
            print(f"❌ Login failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/auth/login"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Login error: {e}")
        results["failed"] += 1
        results["endpoints"]["/auth/login"] = f"❌ ERROR ({e})"
    
    if not token:
        print("❌ Cannot continue without authentication token")
        return results
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get current user
    try:
        response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
        if response.status_code == 200:
            print("✅ Get current user successful")
            results["passed"] += 1
            results["endpoints"]["/auth/me"] = "✅ PASS"
        else:
            print(f"❌ Get current user failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/auth/me"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Get current user error: {e}")
        results["failed"] += 1
        results["endpoints"]["/auth/me"] = f"❌ ERROR ({e})"
    
    # Step 3: Client Management
    print("\n🏢 Testing Client Management...")
    client_id = None
    
    # Create client
    try:
        client_data = {"name": "Test Client", "description": "Test client for endpoint testing"}
        response = requests.post(f"{BASE_URL}/clients/", json=client_data, headers=headers)
        if response.status_code == 200:
            client_id = response.json()["id"]
            print("✅ Create client successful")
            results["passed"] += 1
            results["endpoints"]["/clients/ (POST)"] = "✅ PASS"
        else:
            print(f"❌ Create client failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/clients/ (POST)"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Create client error: {e}")
        results["failed"] += 1
        results["endpoints"]["/clients/ (POST)"] = f"❌ ERROR ({e})"
    
    # Get clients
    try:
        response = requests.get(f"{BASE_URL}/clients/", headers=headers)
        if response.status_code == 200:
            print("✅ Get clients successful")
            results["passed"] += 1
            results["endpoints"]["/clients/ (GET)"] = "✅ PASS"
        else:
            print(f"❌ Get clients failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/clients/ (GET)"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Get clients error: {e}")
        results["failed"] += 1
        results["endpoints"]["/clients/ (GET)"] = f"❌ ERROR ({e})"
    
    # Step 4: Bot Management
    print("\n🤖 Testing Bot Management...")
    bot_id = None
    
    # Create bot
    try:
        bot_data = {
            "meeting_url": "https://meet.google.com/dnm-pkfq-dyi",
            "bot_name": "Endpoint Test Bot"
        }
        response = requests.post(f"{BASE_URL}/create-bot", json=bot_data, headers=headers)
        if response.status_code == 200:
            bot_id = response.json()["bot_id"]
            print("✅ Create bot successful")
            results["passed"] += 1
            results["endpoints"]["/create-bot"] = "✅ PASS"
        else:
            print(f"❌ Create bot failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/create-bot"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Create bot error: {e}")
        results["failed"] += 1
        results["endpoints"]["/create-bot"] = f"❌ ERROR ({e})"
    
    # Get bot status
    if bot_id:
        try:
            response = requests.get(f"{BASE_URL}/bot/{bot_id}/status", headers=headers)
            if response.status_code == 200:
                print("✅ Get bot status successful")
                results["passed"] += 1
                results["endpoints"]["/bot/{id}/status"] = "✅ PASS"
            else:
                print(f"❌ Get bot status failed: {response.status_code}")
                results["failed"] += 1
                results["endpoints"]["/bot/{id}/status"] = f"❌ FAIL ({response.status_code})"
        except Exception as e:
            print(f"❌ Get bot status error: {e}")
            results["failed"] += 1
            results["endpoints"]["/bot/{id}/status"] = f"❌ ERROR ({e})"
    
    # List bots
    try:
        response = requests.get(f"{BASE_URL}/bots", headers=headers)
        if response.status_code == 200:
            print("✅ List bots successful")
            results["passed"] += 1
            results["endpoints"]["/bots (GET)"] = "✅ PASS"
        else:
            print(f"❌ List bots failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/bots (GET)"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ List bots error: {e}")
        results["failed"] += 1
        results["endpoints"]["/bots (GET)"] = f"❌ ERROR ({e})"
    
    # Step 5: Calendar Endpoints (Fixed)
    print("\n📅 Testing Calendar Endpoints...")
    
    # Upcoming meetings
    try:
        response = requests.get(f"{BASE_URL}/calendar/upcoming/1?limit=20", headers=headers)
        if response.status_code == 200:
            print("✅ Get upcoming meetings successful")
            results["passed"] += 1
            results["endpoints"]["/calendar/upcoming/{id}"] = "✅ PASS"
        else:
            print(f"❌ Get upcoming meetings failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/calendar/upcoming/{id}"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Get upcoming meetings error: {e}")
        results["failed"] += 1
        results["endpoints"]["/calendar/upcoming/{id}"] = f"❌ ERROR ({e})"
    
    # Previous meetings
    try:
        response = requests.get(f"{BASE_URL}/calendar/previous/1?limit=20", headers=headers)
        if response.status_code == 200:
            print("✅ Get previous meetings successful")
            results["passed"] += 1
            results["endpoints"]["/calendar/previous/{id}"] = "✅ PASS"
        else:
            print(f"❌ Get previous meetings failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/calendar/previous/{id}"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Get previous meetings error: {e}")
        results["failed"] += 1
        results["endpoints"]["/calendar/previous/{id}"] = f"❌ ERROR ({e})"
    
    # Step 6: AI Generation
    print("\n🤖 Testing AI Generation...")
    if client_id:
        try:
            ai_data = {
                "prompt": "Generate a test email",
                "content_type": "email",
                "client_id": client_id,
                "recipient_name": "Test Client",
                "sender_name": "Test User"
            }
            response = requests.post(f"{BASE_URL}/ai/email", json=ai_data, headers=headers)
            if response.status_code == 200:
                print("✅ Generate email successful")
                results["passed"] += 1
                results["endpoints"]["/ai/email"] = "✅ PASS"
            else:
                print(f"❌ Generate email failed: {response.status_code}")
                results["failed"] += 1
                results["endpoints"]["/ai/email"] = f"❌ FAIL ({response.status_code})"
        except Exception as e:
            print(f"❌ Generate email error: {e}")
            results["failed"] += 1
            results["endpoints"]["/ai/email"] = f"❌ ERROR ({e})"
    
    # Step 7: Debug Endpoints
    print("\n🔧 Testing Debug Endpoints...")
    try:
        response = requests.get(f"{BASE_URL}/debug/users")
        if response.status_code == 200:
            print("✅ Debug users successful")
            results["passed"] += 1
            results["endpoints"]["/debug/users"] = "✅ PASS"
        else:
            print(f"❌ Debug users failed: {response.status_code}")
            results["failed"] += 1
            results["endpoints"]["/debug/users"] = f"❌ FAIL ({response.status_code})"
    except Exception as e:
        print(f"❌ Debug users error: {e}")
        results["failed"] += 1
        results["endpoints"]["/debug/users"] = f"❌ ERROR ({e})"
    
    return results

def print_results(results):
    """Print comprehensive test results"""
    print("\n" + "=" * 60)
    print("📊 ENDPOINT TEST RESULTS")
    print("=" * 60)
    
    print(f"✅ Passed: {results['passed']}")
    print(f"❌ Failed: {results['failed']}")
    print(f"📊 Success Rate: {results['passed']/(results['passed']+results['failed'])*100:.1f}%")
    
    print("\n📋 Detailed Results:")
    for endpoint, status in results["endpoints"].items():
        print(f"   {status} {endpoint}")
    
    if results["failed"] == 0:
        print("\n🎉 ALL ENDPOINTS WORKING! Backend-Frontend integration is ready!")
    else:
        print(f"\n⚠️  {results['failed']} endpoints need attention")

if __name__ == "__main__":
    print("🚀 Starting comprehensive endpoint test...")
    print("Make sure the backend server is running on http://localhost:8000")
    
    try:
        results = test_all_endpoints()
        print_results(results)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
