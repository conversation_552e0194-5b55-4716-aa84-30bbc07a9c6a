import React, { useState, useEffect } from 'react';
import { Brain, Video, FileText, Mail, CheckCircle, Clock, Users, Calendar, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { Navbar } from '../components/Navbar';
import { Button } from '../components/Button';
import { meetingIntelligenceService, MeetingRecording, MeetingResults } from '../services/meetingIntelligence';

export const MeetingIntelligenceDemo: React.FC = () => {
  const [activeRecordings, setActiveRecordings] = useState<MeetingRecording[]>([]);
  const [selectedMeeting, setSelectedMeeting] = useState<string | null>(null);
  const [meetingResults, setMeetingResults] = useState<MeetingResults | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    document.title = 'Meeting Intelligence Demo | Lemur AI';
    loadActiveRecordings();
  }, []);

  const loadActiveRecordings = async () => {
    try {
      const recordings = await meetingIntelligenceService.listActiveMeetings();
      setActiveRecordings(recordings);
    } catch (error) {
      console.error('Error loading recordings:', error);
    }
  };

  const loadMeetingResults = async (meetingId: string) => {
    setIsLoading(true);
    try {
      const results = await meetingIntelligenceService.getMeetingResults(meetingId);
      setMeetingResults(results);
      setSelectedMeeting(meetingId);
    } catch (error) {
      console.error('Error loading meeting results:', error);
      setMeetingResults(null);
    } finally {
      setIsLoading(false);
    }
  };

  const startDemoRecording = async () => {
    try {
      const recording = await meetingIntelligenceService.startMeetingRecording({
        meeting_url: 'https://meet.google.com/demo-meeting-url',
        meeting_title: 'Demo: AI Meeting Intelligence',
        client_id: 'demo-client-1',
        attendees: ['<EMAIL>', '<EMAIL>']
      });

      setActiveRecordings(prev => [...prev, recording]);
    } catch (error) {
      console.error('Error starting demo recording:', error);
    }
  };

  const startDebugRecording = async () => {
    try {
      const result = await meetingIntelligenceService.debugStartMeetingRecording({
        meeting_url: 'https://meet.google.com/debug-meeting-url',
        meeting_title: 'Debug: No Auth Test',
        client_id: 'debug-client',
        attendees: ['<EMAIL>']
      });

      console.log('Debug result:', result);
      alert(`Debug recording ${result.success ? 'started' : 'failed'}: ${result.message || result.error}`);
    } catch (error) {
      console.error('Error starting debug recording:', error);
      alert(`Debug recording failed: ${error.message}`);
    }
  };

  const getStatusIcon = (status: MeetingRecording['status']) => {
    switch (status) {
      case 'recording':
        return <Video className="h-5 w-5 text-red-500 animate-pulse" />;
      case 'processing':
        return <Brain className="h-5 w-5 animate-pulse text-purple-500" />;
      case 'done':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <Clock className="h-5 w-5 text-blue-500" />;
    }
  };

  const getStatusColor = (status: MeetingRecording['status']) => {
    switch (status) {
      case 'recording':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      case 'processing':
        return 'border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-900/20';
      case 'done':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
      default:
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20';
    }
  };

  return (
    <div className="min-h-screen" style={{ background: 'var(--bg-primary)' }}>
      <Navbar />
      
      <main className="container mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Brain className="h-12 w-12 text-purple-600" />
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
                Meeting Intelligence Demo
              </h1>
            </div>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Experience end-to-end AI-powered meeting processing: Bot joins → Records → Transcribes → 
              Generates summaries, action items, and follow-up emails using client context
            </p>
          </div>

          {/* Features Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            {[
              {
                icon: Video,
                title: 'Auto Join & Record',
                description: 'Bot automatically joins meetings and records everything',
                color: 'text-red-500'
              },
              {
                icon: FileText,
                title: 'AI Transcription',
                description: 'High-quality transcription with speaker identification',
                color: 'text-blue-500'
              },
              {
                icon: Brain,
                title: 'Contextual AI',
                description: 'Uses client knowledge base for intelligent content generation',
                color: 'text-purple-500'
              },
              {
                icon: Mail,
                title: 'Auto Follow-up',
                description: 'Generates summaries, action items, and follow-up emails',
                color: 'text-green-500'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                className="text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm"
              >
                <feature.icon className={`h-8 w-8 ${feature.color} mx-auto mb-3`} />
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>

          {/* Demo Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
              Try the Demo
            </h2>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={startDemoRecording}
                leftIcon={<Brain className="h-5 w-5" />}
                className="bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700"
              >
                Start Demo Recording
              </Button>

              <Button
                onClick={startDebugRecording}
                leftIcon={<Video className="h-5 w-5" />}
                className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700"
              >
                Debug (No Auth)
              </Button>

              <Button
                variant="outline"
                onClick={loadActiveRecordings}
                leftIcon={<Clock className="h-5 w-5" />}
              >
                Refresh Status
              </Button>
            </div>
          </div>

          {/* Active Recordings */}
          {activeRecordings.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                Active Recordings
              </h2>
              
              <div className="space-y-4">
                {activeRecordings.map((recording) => (
                  <motion.div
                    key={recording.meeting_id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className={`p-4 rounded-lg border ${getStatusColor(recording.status)}`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {getStatusIcon(recording.status)}
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {recording.meeting_title}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Status: {recording.status} • Started: {new Date(recording.started_at).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {recording.processed && (
                          <Button
                            size="sm"
                            onClick={() => loadMeetingResults(recording.meeting_id)}
                            leftIcon={<ArrowRight className="h-4 w-4" />}
                          >
                            View AI Results
                          </Button>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Meeting Results */}
          {selectedMeeting && meetingResults && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                AI-Generated Results
              </h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Summary */}
                {meetingResults.summary && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-blue-500" />
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        Meeting Summary
                      </h3>
                    </div>
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {meetingResults.summary}
                      </p>
                    </div>
                  </div>
                )}

                {/* Action Items */}
                {meetingResults.action_items && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        Action Items
                      </h3>
                    </div>
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {meetingResults.action_items}
                      </p>
                    </div>
                  </div>
                )}

                {/* Follow-up Email */}
                {meetingResults.follow_up_email && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Mail className="h-5 w-5 text-purple-500" />
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        Follow-up Email
                      </h3>
                    </div>
                    <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {meetingResults.follow_up_email}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Media Links */}
              {(meetingResults.video_url || meetingResults.audio_url) && (
                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
                    Meeting Media
                  </h3>
                  <div className="flex gap-4">
                    {meetingResults.video_url && (
                      <Button
                        variant="outline"
                        size="sm"
                        leftIcon={<Video className="h-4 w-4" />}
                        onClick={() => window.open(meetingResults.video_url, '_blank')}
                      >
                        Download Video
                      </Button>
                    )}
                    {meetingResults.audio_url && (
                      <Button
                        variant="outline"
                        size="sm"
                        leftIcon={<FileText className="h-4 w-4" />}
                        onClick={() => window.open(meetingResults.audio_url, '_blank')}
                      >
                        Download Audio
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {isLoading && (
            <div className="text-center py-8">
              <Brain className="h-8 w-8 animate-spin text-purple-500 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">Loading AI results...</p>
            </div>
          )}
        </motion.div>
      </main>
    </div>
  );
};
