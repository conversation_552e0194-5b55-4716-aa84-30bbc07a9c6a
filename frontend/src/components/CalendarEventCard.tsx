import React from 'react';
import { Calendar, Clock, Video, Users, ExternalLink, Play } from 'lucide-react';
import { Link } from 'react-router-dom';
import { CalendarEvent } from '../services/calendarEvents';
import { cn } from '../utils/cn';
import { formatDate, formatTime } from '../utils/date-utils';
import { Button } from './Button';

interface CalendarEventCardProps {
  event: CalendarEvent;
  className?: string;
}

export const CalendarEventCard: React.FC<CalendarEventCardProps> = ({ event, className }) => {
  const { id, title, startTime, endTime, attendees, meetingLink } = event;

  const formattedDate = formatDate(startTime);
  const formattedStartTime = formatTime(startTime);
  const formattedEndTime = formatTime(endTime);
  
  // Calculate duration in minutes
  const duration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

  // Determine if this is an upcoming meeting
  const isUpcoming = startTime > new Date();
  const isToday = startTime.toDateString() === new Date().toDateString();

  const handleJoinMeeting = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (meetingLink) {
      window.open(meetingLink, '_blank');
    }
  };

  const handleCardClick = () => {
    // Navigate to appropriate page based on meeting status
    if (isUpcoming) {
      // Store meeting context for upcoming meeting page
      const meetingContext = {
        fromCalendar: false,
        meeting: event,
        returnMessage: "You can return to dashboard once you're done preparing"
      };
      sessionStorage.setItem('meetingContext', JSON.stringify(meetingContext));
      window.location.href = `/meeting/upcoming/${id}`;
    } else {
      // For past meetings, go to regular meeting details if available
      window.location.href = `/meetings/${id}`;
    }
  };

  const getStatusBadge = () => {
    if (isToday) {
      return (
        <span className="rounded-full px-3 py-1.5 text-xs font-semibold bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
          Today
        </span>
      );
    } else if (isUpcoming) {
      return (
        <span className="rounded-full px-3 py-1.5 text-xs font-semibold bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">
          Upcoming
        </span>
      );
    } else {
      return (
        <span className="rounded-full px-3 py-1.5 text-xs font-semibold bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
          Completed
        </span>
      );
    }
  };

  return (
    <div
      className={cn(
        'card group relative flex h-full min-h-[280px] flex-col justify-between hover-lift animate-fade-in',
        'transition-all duration-300 ease-out cursor-pointer',
        className
      )}
      onClick={handleCardClick}
    >
      {/* Header */}
      <div className="flex-1">
        <div className="flex items-start justify-between mb-3">
          <h3
            className="font-semibold line-clamp-2 pr-2 leading-tight transition-colors duration-200"
            style={{ color: 'var(--text-primary)' }}
          >
            {title}
          </h3>
          {getStatusBadge()}
        </div>

        {/* Meeting Details */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center gap-3 text-sm transition-colors duration-200" style={{ color: 'var(--text-secondary)' }}>
            <Calendar className="h-4 w-4 flex-shrink-0" style={{ color: 'var(--text-accent)' }} />
            <span className="truncate font-medium">{formattedDate}</span>
          </div>

          <div className="flex items-center gap-3 text-sm transition-colors duration-200" style={{ color: 'var(--text-secondary)' }}>
            <Clock className="h-4 w-4 flex-shrink-0" style={{ color: 'var(--text-accent)' }} />
            <span className="truncate">
              {formattedStartTime} - {formattedEndTime} ({duration} min)
            </span>
          </div>

          {meetingLink && (
            <div className="flex items-center gap-3 text-sm transition-colors duration-200" style={{ color: 'var(--text-secondary)' }}>
              <Video className="h-4 w-4 flex-shrink-0 text-blue-600 dark:text-blue-400" />
              <span className="truncate">
                Video Meeting
              </span>
            </div>
          )}

          <div className="flex items-center gap-3 text-sm transition-colors duration-200" style={{ color: 'var(--text-secondary)' }}>
            <Users className="h-4 w-4 flex-shrink-0" style={{ color: 'var(--text-accent)' }} />
            <span className="truncate">
              {attendees.length} participant{attendees.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
      </div>

      {/* Attendees Section */}
      <div className="mb-4">
        {attendees.length > 0 && (
          <div className="flex items-center gap-3">
            <div className="flex -space-x-2">
              {attendees.slice(0, 3).map((attendee, index) => {
                const name = typeof attendee === 'string' ? attendee.split('@')[0] : attendee.name || attendee.email;
                return (
                  <div
                    key={index}
                    className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-sm font-medium text-primary-700 ring-2 ring-white dark:bg-primary-900 dark:text-primary-300 dark:ring-dark-900"
                    style={{ zIndex: 10 - index }}
                  >
                    {name.charAt(0).toUpperCase()}
                  </div>
                );
              })}
              {attendees.length > 3 && (
                <div className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-dark-200 text-xs font-medium text-dark-600 ring-2 ring-white dark:bg-dark-700 dark:text-dark-300 dark:ring-dark-900">
                  +{attendees.length - 3}
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-dark-900 dark:text-dark-50 truncate">
                {typeof attendees[0] === 'string' ? attendees[0].split('@')[0] : attendees[0].name || attendees[0].email}
              </p>
              {attendees.length > 1 && (
                <p className="text-xs text-dark-500 dark:text-dark-400 truncate">
                  +{attendees.length - 1} others
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="flex-1">
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            leftIcon={<ExternalLink className="h-4 w-4" />}
            onClick={(e) => {
              e.stopPropagation();
              handleCardClick();
            }}
          >
            {isUpcoming ? 'Prepare' : 'View Details'}
          </Button>
        </div>

        {meetingLink && (isUpcoming || isToday) && (
          <Button
            size="sm"
            onClick={handleJoinMeeting}
            leftIcon={<Play className="h-4 w-4" />}
            className="flex-shrink-0"
          >
            Join
          </Button>
        )}
      </div>
    </div>
  );
};
