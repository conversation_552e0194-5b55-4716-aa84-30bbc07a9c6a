import React, { useState, useEffect } from 'react';
import { X, Calendar, Clock, Users, Video, Edit3, Save, ArrowLeft } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from './Button';
import { CalendarEvent } from '../services/calendarEvents';

interface MeetingDetailModalProps {
  meeting: CalendarEvent | null;
  isOpen: boolean;
  onClose: () => void;
  fromCalendar?: boolean;
}

export const MeetingDetailModal: React.FC<MeetingDetailModalProps> = ({
  meeting,
  isOpen,
  onClose,
  fromCalendar = false
}) => {
  const [meetingNotes, setMeetingNotes] = useState('');
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [tempNotes, setTempNotes] = useState('');

  useEffect(() => {
    if (meeting) {
      // Load existing notes from localStorage
      const savedNotes = localStorage.getItem(`meeting-notes-${meeting.id}`);
      setMeetingNotes(savedNotes || '');
      setTempNotes(savedNotes || '');
    }
  }, [meeting]);

  const handleSaveNotes = () => {
    if (meeting) {
      localStorage.setItem(`meeting-notes-${meeting.id}`, tempNotes);
      setMeetingNotes(tempNotes);
      setIsEditingNotes(false);
    }
  };

  const handleCancelEdit = () => {
    setTempNotes(meetingNotes);
    setIsEditingNotes(false);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isPast = meeting ? meeting.startTime < new Date() : false;
  const isToday = meeting ? meeting.startTime.toDateString() === new Date().toDateString() : false;
  const isUpcoming = meeting ? meeting.startTime > new Date() && !isToday : false;

  if (!meeting) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-2xl mx-4 bg-white rounded-xl shadow-2xl overflow-hidden"
            style={{ background: 'var(--bg-primary)' }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: 'var(--border-primary)' }}>
              <div className="flex items-center gap-3">
                {fromCalendar && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    leftIcon={<ArrowLeft className="h-4 w-4" />}
                  >
                    Back to Calendar
                  </Button>
                )}
                <div>
                  <h2 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                    {meeting.title}
                  </h2>
                  <div className="flex items-center gap-2 mt-1">
                    {isPast && (
                      <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                        Completed
                      </span>
                    )}
                    {isToday && (
                      <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400">
                        Today
                      </span>
                    )}
                    {isUpcoming && (
                      <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400">
                        Upcoming
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                leftIcon={<X className="h-4 w-4" />}
              />
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Meeting Info */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5" style={{ color: 'var(--text-secondary)' }} />
                  <div>
                    <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Date</p>
                    <p className="font-medium" style={{ color: 'var(--text-primary)' }}>
                      {formatDate(meeting.startTime)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5" style={{ color: 'var(--text-secondary)' }} />
                  <div>
                    <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Time</p>
                    <p className="font-medium" style={{ color: 'var(--text-primary)' }}>
                      {formatTime(meeting.startTime)} - {formatTime(meeting.endTime)}
                    </p>
                  </div>
                </div>

                {meeting.attendees.length > 0 && (
                  <div className="flex items-center gap-3">
                    <Users className="h-5 w-5" style={{ color: 'var(--text-secondary)' }} />
                    <div>
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Attendees</p>
                      <p className="font-medium" style={{ color: 'var(--text-primary)' }}>
                        {meeting.attendees.length} people
                      </p>
                    </div>
                  </div>
                )}

                {meeting.meetingLink && (
                  <div className="flex items-center gap-3">
                    <Video className="h-5 w-5" style={{ color: 'var(--text-secondary)' }} />
                    <div>
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Meeting Link</p>
                      <a
                        href={meeting.meetingLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        Join Meeting
                      </a>
                    </div>
                  </div>
                )}
              </div>

              {/* Return Message for Calendar Navigation */}
              {fromCalendar && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    💡 You can come back to the calendar once you're done with the meeting
                  </p>
                </div>
              )}

              {/* Meeting Notes */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>
                    Things to talk about in the meeting
                  </h3>
                  {!isEditingNotes && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsEditingNotes(true)}
                      leftIcon={<Edit3 className="h-4 w-4" />}
                    >
                      Edit
                    </Button>
                  )}
                </div>

                {isEditingNotes ? (
                  <div className="space-y-3">
                    <textarea
                      value={tempNotes}
                      onChange={(e) => setTempNotes(e.target.value)}
                      placeholder="Add notes about what to discuss in this meeting..."
                      className="w-full h-32 p-3 border rounded-lg resize-none"
                      style={{
                        background: 'var(--bg-secondary)',
                        borderColor: 'var(--border-primary)',
                        color: 'var(--text-primary)'
                      }}
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={handleSaveNotes}
                        leftIcon={<Save className="h-4 w-4" />}
                        size="sm"
                      >
                        Save Notes
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleCancelEdit}
                        size="sm"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div
                    className="p-4 rounded-lg min-h-[100px] cursor-pointer hover:bg-opacity-80 transition-colors"
                    style={{ background: 'var(--bg-secondary)' }}
                    onClick={() => setIsEditingNotes(true)}
                  >
                    {meetingNotes ? (
                      <p style={{ color: 'var(--text-primary)' }} className="whitespace-pre-wrap">
                        {meetingNotes}
                      </p>
                    ) : (
                      <p style={{ color: 'var(--text-secondary)' }} className="italic">
                        Click to add notes about what to discuss in this meeting...
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};
