import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Client } from '../types';
import { clients as initialClients } from '../data/clients';
import { MLPipelineService, MLClient, MLSubClient, MLFile } from '../services/mlPipeline';

interface ClientStore {
  clients: Client[];
  mlClients: MLClient[];
  subClients: Record<string, MLSubClient[]>;
  clientFiles: Record<string, MLFile[]>;
  isLoading: boolean;
  error: string | null;

  // Legacy Actions (for backward compatibility)
  addClient: (client: Client) => void;
  updateClient: (clientId: string, updates: Partial<Client>) => void;
  deleteClient: (clientId: string) => void;
  getClient: (clientId: string) => Client | undefined;
  initializeClients: () => void;

  // New ML Pipeline Actions (Centralized Brain)
  createMLClient: (name: string, description?: string) => Promise<MLClient>;
  fetchMLClients: () => Promise<void>;
  createSubClient: (clientId: string, name: string, description?: string, contactEmail?: string, contactName?: string) => Promise<MLSubClient>;
  fetchSubClients: (clientId: string) => Promise<void>;
  uploadFile: (clientId: string, file: File, subClientId?: string) => Promise<MLFile>;

  // AI-Powered Actions
  generateFollowUp: (clientId: string, meetingNotes: string, recipientName: string, senderName: string, subClientId?: string) => Promise<string>;
  generateProposal: (clientId: string, projectDetails: string, subClientId?: string) => Promise<string>;
  generateScopeOfWork: (clientId: string, requirements: string, subClientId?: string) => Promise<string>;
  searchKnowledge: (clientId: string, query: string, subClientId?: string) => Promise<any>;
}

export const useClientStore = create<ClientStore>()(
  persist(
    (set, get) => ({
      clients: [],
      mlClients: [],
      subClients: {},
      clientFiles: {},
      isLoading: false,
      error: null,

      addClient: (client) => {
        set((state) => ({
          clients: [...state.clients, {
            ...client,
            createdAt: new Date(),
            updatedAt: new Date()
          }],
        }));
      },

      updateClient: (clientId, updates) => {
        set((state) => ({
          clients: state.clients.map((client) =>
            client.id === clientId 
              ? { ...client, ...updates, updatedAt: new Date() } 
              : client
          ),
        }));
      },

      deleteClient: (clientId) => {
        set((state) => ({
          clients: state.clients.filter((client) => client.id !== clientId),
        }));
      },

      getClient: (clientId) => {
        const state = get();
        return state.clients.find((client) => client.id === clientId);
      },

      initializeClients: () => {
        const state = get();
        if (state.clients.length === 0) {
          set({ clients: initialClients });
        }
        // Also fetch ML clients from backend
        get().fetchMLClients();
      },

      // ============================================================================
      // ML PIPELINE ACTIONS (Centralized Brain)
      // ============================================================================

      createMLClient: async (name: string, description?: string) => {
        set({ isLoading: true, error: null });
        try {
          const mlClient = await MLPipelineService.createClient(name, description);
          set((state) => ({
            mlClients: [...state.mlClients, mlClient],
            isLoading: false
          }));
          return mlClient;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      fetchMLClients: async () => {
        set({ isLoading: true, error: null });
        try {
          const mlClients = await MLPipelineService.getClients();
          set({ mlClients, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      createSubClient: async (clientId: string, name: string, description?: string, contactEmail?: string, contactName?: string) => {
        set({ isLoading: true, error: null });
        try {
          const subClient = await MLPipelineService.createSubClient(clientId, name, description, contactEmail, contactName);
          set((state) => ({
            subClients: {
              ...state.subClients,
              [clientId]: [...(state.subClients[clientId] || []), subClient]
            },
            isLoading: false
          }));
          return subClient;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      fetchSubClients: async (clientId: string) => {
        set({ isLoading: true, error: null });
        try {
          const subClients = await MLPipelineService.getSubClients(clientId);
          set((state) => ({
            subClients: {
              ...state.subClients,
              [clientId]: subClients
            },
            isLoading: false
          }));
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      uploadFile: async (clientId: string, file: File, subClientId?: string) => {
        set({ isLoading: true, error: null });
        try {
          const uploadedFile = await MLPipelineService.uploadFile(clientId, file, subClientId);
          set((state) => ({
            clientFiles: {
              ...state.clientFiles,
              [clientId]: [...(state.clientFiles[clientId] || []), uploadedFile]
            },
            isLoading: false
          }));
          return uploadedFile;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      // ============================================================================
      // AI-POWERED ACTIONS (Smart Follow-ups & Proposals)
      // ============================================================================

      generateFollowUp: async (clientId: string, meetingNotes: string, recipientName: string, senderName: string, subClientId?: string) => {
        set({ isLoading: true, error: null });
        try {
          const result = await MLPipelineService.generateFollowUpEmail(clientId, meetingNotes, recipientName, senderName, subClientId);
          set({ isLoading: false });
          return result.content;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      generateProposal: async (clientId: string, projectDetails: string, subClientId?: string) => {
        set({ isLoading: true, error: null });
        try {
          const result = await MLPipelineService.generateProposal(clientId, projectDetails, subClientId);
          set({ isLoading: false });
          return result.content;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      generateScopeOfWork: async (clientId: string, requirements: string, subClientId?: string) => {
        set({ isLoading: true, error: null });
        try {
          const result = await MLPipelineService.generateScopeOfWork(clientId, requirements, subClientId);
          set({ isLoading: false });
          return result.content;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      searchKnowledge: async (clientId: string, query: string, subClientId?: string) => {
        set({ isLoading: true, error: null });
        try {
          const result = await MLPipelineService.searchKnowledge({
            query,
            client_id: clientId,
            sub_client_id: subClientId,
            n_results: 5
          });
          set({ isLoading: false });
          return result;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },
    }),
    {
      name: 'client-storage',
      version: 2, // Increment version for new fields
    }
  )
);
