#!/usr/bin/env python3
"""
Test RLS fix by trying different approaches
"""

import sys
from dotenv import load_dotenv
from datetime import datetime
import uuid

# Load environment variables
load_dotenv()

# Add Backend_Lemur_Waitlist to path
sys.path.append('./Backend_Lemur_Waitlist')

def test_rls_fix():
    print("🔍 Testing RLS fix approaches...")
    
    try:
        from Backend_Lemur_Waitlist.database import get_supabase
        
        supabase = get_supabase()
        if not supabase:
            print("❌ Supabase connection failed")
            return
        
        print("✅ Supabase connection successful")
        
        # Get a valid client ID and user ID
        clients_result = supabase.table("clients").select("*").limit(1).execute()
        if not clients_result.data:
            print("❌ No clients found")
            return
            
        client_id = clients_result.data[0]["id"]
        user_id = "4a690bf1-9f02-4508-8612-e07c76524160"  # Aditi's ID
        
        print(f"Using client_id: {client_id}")
        print(f"Using user_id: {user_id}")
        
        # Test 1: Try sub-client creation with minimal data
        print(f"\n🧪 Test 1: Minimal sub-client creation...")
        
        minimal_sub_client = {
            "name": "Minimal Test Sub-Client",
            "client_id": client_id,
            "is_active": True
        }
        
        try:
            result = supabase.table("sub_clients").insert(minimal_sub_client).execute()
            if result.data:
                print(f"   ✅ Minimal sub-client created: {result.data[0]['id']}")
            else:
                print(f"   ❌ Minimal sub-client creation failed: {result}")
        except Exception as e:
            print(f"   ❌ Minimal sub-client error: {e}")
        
        # Test 2: Try with explicit timestamps
        print(f"\n🧪 Test 2: Sub-client with explicit timestamps...")
        
        timestamped_sub_client = {
            "name": "Timestamped Test Sub-Client",
            "client_id": client_id,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_active": True
        }
        
        try:
            result = supabase.table("sub_clients").insert(timestamped_sub_client).execute()
            if result.data:
                print(f"   ✅ Timestamped sub-client created: {result.data[0]['id']}")
            else:
                print(f"   ❌ Timestamped sub-client creation failed: {result}")
        except Exception as e:
            print(f"   ❌ Timestamped sub-client error: {e}")
        
        # Test 3: Try file creation with minimal data
        print(f"\n🧪 Test 3: Minimal file creation...")
        
        minimal_file = {
            "filename": f"test_{uuid.uuid4()}.txt",
            "original_filename": "test.txt",
            "file_type": ".txt",
            "file_size": 1024,
            "storage_path": "uploads/test.txt",
            "client_id": client_id,
            "user_id": user_id,
            "processed": False
        }
        
        try:
            result = supabase.table("files").insert(minimal_file).execute()
            if result.data:
                print(f"   ✅ Minimal file created: {result.data[0]['id']}")
            else:
                print(f"   ❌ Minimal file creation failed: {result}")
        except Exception as e:
            print(f"   ❌ Minimal file error: {e}")
        
        # Test 4: Check RLS policies
        print(f"\n🧪 Test 4: Checking RLS policies...")
        
        try:
            # Try to query RLS policies (this might not work with anon key)
            policies_result = supabase.rpc('get_policies').execute()
            print(f"   Policies: {policies_result.data}")
        except Exception as e:
            print(f"   Cannot check policies: {e}")
        
        # Test 5: Try using upsert instead of insert
        print(f"\n🧪 Test 5: Using upsert for sub-client...")
        
        upsert_sub_client = {
            "id": str(uuid.uuid4()),
            "name": "Upsert Test Sub-Client",
            "client_id": client_id,
            "is_active": True
        }
        
        try:
            result = supabase.table("sub_clients").upsert(upsert_sub_client).execute()
            if result.data:
                print(f"   ✅ Upsert sub-client created: {result.data[0]['id']}")
            else:
                print(f"   ❌ Upsert sub-client creation failed: {result}")
        except Exception as e:
            print(f"   ❌ Upsert sub-client error: {e}")
                
    except Exception as e:
        print(f"❌ Error in RLS test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_rls_fix()
