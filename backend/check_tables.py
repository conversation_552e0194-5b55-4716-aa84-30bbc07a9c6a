#!/usr/bin/env python3
"""
Check database table structure and test insertions
"""

import sys
from dotenv import load_dotenv
from datetime import datetime
import uuid

# Load environment variables
load_dotenv()

# Add Backend_Lemur_Waitlist to path
sys.path.append('./Backend_Lemur_Waitlist')

def check_tables():
    print("🔍 Checking database tables...")
    
    try:
        from Backend_Lemur_Waitlist.database import get_supabase
        
        supabase = get_supabase()
        if not supabase:
            print("❌ Supabase connection failed")
            return
        
        print("✅ Supabase connection successful")
        
        # Test each table
        tables_to_check = [
            "users",
            "clients", 
            "sub_clients",
            "files",
            "outputs"
        ]
        
        for table_name in tables_to_check:
            print(f"\n📊 Checking table: {table_name}")
            try:
                result = supabase.table(table_name).select("*").limit(5).execute()
                print(f"   ✅ Table exists, {len(result.data)} records found")
                
                if result.data:
                    # Show first record structure
                    first_record = result.data[0]
                    print(f"   📋 Sample record keys: {list(first_record.keys())}")
                    
            except Exception as e:
                print(f"   ❌ Table error: {e}")
        
        # Test sub-client creation specifically
        print(f"\n🧪 Testing sub-client creation...")
        
        # Get a client ID first
        clients_result = supabase.table("clients").select("*").limit(1).execute()
        if not clients_result.data:
            print("   ❌ No clients found for testing")
            return
            
        client_id = clients_result.data[0]["id"]
        print(f"   Using client ID: {client_id}")
        
        sub_client_data = {
            "name": "Test Sub-Client",
            "description": "Testing sub-client creation",
            "client_id": client_id,
            "contact_email": "<EMAIL>",
            "contact_name": "Test Contact",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_active": True
        }
        
        try:
            sub_result = supabase.table("sub_clients").insert(sub_client_data).execute()
            if sub_result.data:
                print(f"   ✅ Sub-client created: {sub_result.data[0]['id']}")
            else:
                print(f"   ❌ Sub-client creation failed: {sub_result}")
        except Exception as e:
            print(f"   ❌ Sub-client creation error: {e}")
        
        # Test file record creation
        print(f"\n🧪 Testing file record creation...")
        
        file_data = {
            "filename": f"test_{uuid.uuid4()}.txt",
            "original_filename": "test.txt",
            "file_type": ".txt",
            "file_size": 1024,
            "storage_path": "uploads/test.txt",
            "client_id": client_id,
            "user_id": "4a690bf1-9f02-4508-8612-e07c76524160",  # Aditi's ID
            "processed": False,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        try:
            file_result = supabase.table("files").insert(file_data).execute()
            if file_result.data:
                print(f"   ✅ File record created: {file_result.data[0]['id']}")
            else:
                print(f"   ❌ File record creation failed: {file_result}")
        except Exception as e:
            print(f"   ❌ File record creation error: {e}")
        
        # Test output creation
        print(f"\n🧪 Testing output creation...")
        
        output_data = {
            "title": "Test Output",
            "content": "This is a test output content",
            "output_type": "summary",
            "prompt": "Test prompt",
            "client_id": client_id,
            "user_id": "4a690bf1-9f02-4508-8612-e07c76524160",  # Aditi's ID
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        try:
            output_result = supabase.table("outputs").insert(output_data).execute()
            if output_result.data:
                print(f"   ✅ Output created: {output_result.data[0]['id']}")
            else:
                print(f"   ❌ Output creation failed: {output_result}")
        except Exception as e:
            print(f"   ❌ Output creation error: {e}")
                
    except Exception as e:
        print(f"❌ Error checking tables: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_tables()
