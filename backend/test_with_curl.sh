#!/bin/bash

# Comprehensive curl test script for ML Pipeline integration
# Tests the complete "Centralized Brain" functionality

BASE_URL="http://localhost:8000"
TOKEN=""
USER_ID=""
CLIENT_ID=""
SUB_CLIENT_ID=""
FILE_ID=""

echo "🚀 Testing ML Pipeline Integration with curl"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to extract JSON value
extract_json() {
    echo "$1" | grep -o "\"$2\":[^,}]*" | cut -d':' -f2 | tr -d '"' | tr -d ' '
}

echo -e "${BLUE}📋 Step 1: Health Check${NC}"
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$BASE_URL/health")
HTTP_CODE="${HEALTH_RESPONSE: -3}"
if [ "$HTTP_CODE" = "200" ]; then
    print_result 0 "Health check passed"
    cat /tmp/health_response.json | jq '.' 2>/dev/null || cat /tmp/health_response.json
else
    print_result 1 "Health check failed (HTTP $HTTP_CODE)"
    exit 1
fi

echo -e "\n${BLUE}📋 Step 2: User Authentication${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "synatech@Aditi"
    }')

echo "Login response: $LOGIN_RESPONSE"

# Extract token and user_id
TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
USER_ID=$(echo "$LOGIN_RESPONSE" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -n "$TOKEN" ] && [ -n "$USER_ID" ]; then
    print_result 0 "Authentication successful"
    echo "User ID: $USER_ID"
    echo "Token: ${TOKEN:0:20}..."
else
    print_result 1 "Authentication failed"
    echo "Response: $LOGIN_RESPONSE"
    exit 1
fi

echo -e "\n${BLUE}📋 Step 3: Create Client (Centralized Brain)${NC}"
CLIENT_RESPONSE=$(curl -s -X POST "$BASE_URL/clients" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
        "name": "TechCorp Solutions",
        "description": "AI consulting client with centralized knowledge base"
    }')

echo "Client creation response: $CLIENT_RESPONSE"

CLIENT_ID=$(echo "$CLIENT_RESPONSE" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -n "$CLIENT_ID" ]; then
    print_result 0 "Client created successfully"
    echo "Client ID: $CLIENT_ID"
else
    print_result 1 "Client creation failed"
    echo "Response: $CLIENT_RESPONSE"
    exit 1
fi

echo -e "\n${BLUE}📋 Step 4: Get All Clients${NC}"
CLIENTS_RESPONSE=$(curl -s -X GET "$BASE_URL/clients" \
    -H "Authorization: Bearer $TOKEN")

echo "Clients list: $CLIENTS_RESPONSE"
CLIENT_COUNT=$(echo "$CLIENTS_RESPONSE" | grep -o '"id":' | wc -l)
print_result 0 "Retrieved $CLIENT_COUNT clients"

echo -e "\n${BLUE}📋 Step 5: Create Sub-Client (Project/Department)${NC}"
SUB_CLIENT_RESPONSE=$(curl -s -X POST "$BASE_URL/clients/$CLIENT_ID/sub-clients" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
        "name": "AI Development Team",
        "description": "Machine learning and AI development projects",
        "contact_email": "<EMAIL>",
        "contact_name": "Sarah Johnson"
    }')

echo "Sub-client creation response: $SUB_CLIENT_RESPONSE"

SUB_CLIENT_ID=$(echo "$SUB_CLIENT_RESPONSE" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -n "$SUB_CLIENT_ID" ]; then
    print_result 0 "Sub-client created successfully"
    echo "Sub-client ID: $SUB_CLIENT_ID"
else
    print_result 1 "Sub-client creation failed"
    echo "Response: $SUB_CLIENT_RESPONSE"
fi

echo -e "\n${BLUE}📋 Step 6: Get Sub-Clients${NC}"
SUB_CLIENTS_RESPONSE=$(curl -s -X GET "$BASE_URL/clients/$CLIENT_ID/sub-clients" \
    -H "Authorization: Bearer $TOKEN")

echo "Sub-clients list: $SUB_CLIENTS_RESPONSE"
SUB_CLIENT_COUNT=$(echo "$SUB_CLIENTS_RESPONSE" | grep -o '"id":' | wc -l)
print_result 0 "Retrieved $SUB_CLIENT_COUNT sub-clients"

echo -e "\n${BLUE}📋 Step 7: Upload File (Build Knowledge Base)${NC}"
# Create a test file
cat > /tmp/test_project.txt << EOF
Project: AI Customer Service Platform
Client: TechCorp Solutions
Team: AI Development Team

Project Overview:
We are developing an AI-powered customer service platform that uses natural language processing to understand customer queries and provide intelligent responses.

Key Requirements:
1. Real-time chat interface
2. Sentiment analysis
3. Automated ticket routing
4. Knowledge base integration
5. Multi-language support

Technical Stack:
- Frontend: React.js with TypeScript
- Backend: Python FastAPI
- AI/ML: OpenAI GPT-4, Hugging Face Transformers
- Database: PostgreSQL with vector extensions
- Deployment: Docker, Kubernetes

Success Metrics:
- 90% customer satisfaction score
- 50% reduction in response time
- 30% decrease in human agent workload

Budget: $150,000
Timeline: 6 months
Team Size: 5 developers

Previous Similar Projects:
- ChatBot Pro for FinanceFirst (2023) - 95% satisfaction
- AI Support for RetailMax (2022) - 60% efficiency gain
EOF

FILE_RESPONSE=$(curl -s -X POST "$BASE_URL/clients/$CLIENT_ID/files" \
    -H "Authorization: Bearer $TOKEN" \
    -F "file=@/tmp/test_project.txt" \
    -F "sub_client_id=$SUB_CLIENT_ID")

echo "File upload response: $FILE_RESPONSE"

FILE_ID=$(echo "$FILE_RESPONSE" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -n "$FILE_ID" ]; then
    print_result 0 "File uploaded and processed successfully"
    echo "File ID: $FILE_ID"
else
    print_result 1 "File upload failed"
    echo "Response: $FILE_RESPONSE"
fi

echo -e "\n${BLUE}📋 Step 8: Search Knowledge Base${NC}"
SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/knowledge/search" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
        "query": "AI customer service platform requirements and budget",
        "client_id": "'$CLIENT_ID'",
        "sub_client_id": "'$SUB_CLIENT_ID'",
        "n_results": 3
    }')

echo "Knowledge search response: $SEARCH_RESPONSE"

SEARCH_RESULTS=$(echo "$SEARCH_RESPONSE" | grep -o '"total_results":[0-9]*' | cut -d':' -f2)

if [ -n "$SEARCH_RESULTS" ] && [ "$SEARCH_RESULTS" -gt 0 ]; then
    print_result 0 "Knowledge search successful ($SEARCH_RESULTS results)"
else
    print_result 1 "Knowledge search failed or no results"
fi

echo -e "\n${BLUE}📋 Step 9: Generate AI Follow-up Email${NC}"
EMAIL_RESPONSE=$(curl -s -X POST "$BASE_URL/llm/generate" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
        "prompt": "Generate a follow-up email after our meeting about the AI customer service platform project",
        "content_type": "email",
        "client_id": "'$CLIENT_ID'",
        "sub_client_id": "'$SUB_CLIENT_ID'",
        "recipient_name": "Sarah Johnson",
        "sender_name": "Aditi Sharma",
        "additional_instructions": "Reference the project requirements and budget from our knowledge base"
    }')

echo "AI email generation response: $EMAIL_RESPONSE"

EMAIL_CONTENT=$(echo "$EMAIL_RESPONSE" | grep -o '"content":"[^"]*' | cut -d'"' -f4)

if [ -n "$EMAIL_CONTENT" ]; then
    print_result 0 "AI follow-up email generated successfully"
    echo "Email preview: ${EMAIL_CONTENT:0:100}..."
else
    print_result 1 "AI email generation failed"
    echo "Response: $EMAIL_RESPONSE"
fi

echo -e "\n${BLUE}📋 Step 10: Generate Proposal Draft${NC}"
PROPOSAL_RESPONSE=$(curl -s -X POST "$BASE_URL/llm/generate" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
        "prompt": "Create a detailed proposal for the AI customer service platform project",
        "content_type": "proposal",
        "client_id": "'$CLIENT_ID'",
        "sub_client_id": "'$SUB_CLIENT_ID'",
        "additional_instructions": "Include technical approach, timeline, budget breakdown, and reference our successful similar projects"
    }')

echo "AI proposal generation response: $PROPOSAL_RESPONSE"

PROPOSAL_CONTENT=$(echo "$PROPOSAL_RESPONSE" | grep -o '"content":"[^"]*' | cut -d'"' -f4)

if [ -n "$PROPOSAL_CONTENT" ]; then
    print_result 0 "AI proposal generated successfully"
    echo "Proposal preview: ${PROPOSAL_CONTENT:0:100}..."
else
    print_result 1 "AI proposal generation failed"
    echo "Response: $PROPOSAL_RESPONSE"
fi

echo -e "\n${BLUE}📋 Step 11: Generate Scope of Work${NC}"
SOW_RESPONSE=$(curl -s -X POST "$BASE_URL/llm/generate" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
        "prompt": "Create a comprehensive scope of work document for the AI customer service platform",
        "content_type": "scope_of_work",
        "client_id": "'$CLIENT_ID'",
        "sub_client_id": "'$SUB_CLIENT_ID'",
        "additional_instructions": "Include deliverables, milestones, acceptance criteria, and risk mitigation based on our past project experience"
    }')

echo "Scope of work generation response: $SOW_RESPONSE"

SOW_CONTENT=$(echo "$SOW_RESPONSE" | grep -o '"content":"[^"]*' | cut -d'"' -f4)

if [ -n "$SOW_CONTENT" ]; then
    print_result 0 "Scope of work generated successfully"
    echo "SOW preview: ${SOW_CONTENT:0:100}..."
else
    print_result 1 "Scope of work generation failed"
    echo "Response: $SOW_RESPONSE"
fi

echo -e "\n${GREEN}🎉 ML Pipeline Integration Test Complete!${NC}"
echo "=============================================="
echo -e "${YELLOW}Summary:${NC}"
echo "✅ Health Check"
echo "✅ User Authentication"
echo "✅ Client Management (Centralized Brain)"
echo "✅ Sub-Client Management"
echo "✅ Knowledge Base (File Upload & Processing)"
echo "✅ Intelligent Search"
echo "✅ AI Follow-up Email Generation"
echo "✅ AI Proposal Generation"
echo "✅ AI Scope of Work Generation"

echo -e "\n${BLUE}🧠 Your Centralized Brain is now fully operational!${NC}"
echo "Each client has their own knowledge base that powers AI content generation."

# Clean up
rm -f /tmp/test_project.txt /tmp/health_response.json
