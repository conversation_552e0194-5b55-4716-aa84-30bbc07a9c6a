#!/usr/bin/env python3
"""
Test client creation specifically to debug the issue
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "synatech@Aditi"

def test_client_creation():
    print("🔍 Testing client creation specifically...")
    
    # 1. Login first
    print("1. Logging in...")
    login_data = {
        "email": TEST_USER_EMAIL,
        "password": TEST_USER_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return
    
    data = response.json()
    token = data["access_token"]
    user_id = data["user"]["id"]
    print(f"✅ Login successful! User ID: {user_id}")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 2. Test client creation with detailed error handling
    print("\n2. Testing client creation...")
    client_data = {
        "name": "Debug Test Client",
        "description": "Testing client creation with detailed error handling"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/clients",
            json=client_data,
            headers=headers
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            client = response.json()
            print(f"✅ Client created successfully!")
            print(f"   ID: {client['id']}")
            print(f"   Name: {client['name']}")
            print(f"   User ID: {client['user_id']}")
        else:
            print(f"❌ Client creation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   Raw error: {response.text}")
                
    except Exception as e:
        print(f"❌ Request failed with exception: {e}")
    
    # 3. Test direct database access
    print("\n3. Testing direct database access...")
    try:
        import sys
        sys.path.append('./Backend_Lemur_Waitlist')
        from dotenv import load_dotenv
        load_dotenv()
        
        from Backend_Lemur_Waitlist.database import get_supabase, DatabaseOperations, Client as DBClient
        from datetime import datetime
        
        supabase = get_supabase()
        if supabase:
            print("✅ Supabase connection successful")
            
            # Try to create client directly
            client_record = {
                "name": "Direct DB Test Client",
                "description": "Testing direct database insertion",
                "user_id": user_id,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "is_active": True
            }
            
            print(f"   Attempting to insert: {client_record}")
            result = supabase.table("clients").insert(client_record).execute()
            
            if result.data:
                print(f"✅ Direct database insertion successful: {result.data[0]}")
            else:
                print(f"❌ Direct database insertion failed: {result}")
                
        else:
            print("❌ Supabase connection failed")
            
    except Exception as e:
        print(f"❌ Direct database test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_client_creation()
