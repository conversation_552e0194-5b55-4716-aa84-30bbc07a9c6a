-- SQL script to fix RLS policies for seamless operation
-- Run this in your Supabase SQL editor

-- 1. Temporarily disable <PERSON><PERSON> for development (re-enable in production)
ALTER TABLE sub_clients DISABLE ROW LEVEL SECURITY;
ALTER TABLE files DISABLE ROW LEVEL SECURITY;
ALTER TABLE outputs DISABLE ROW LEVEL SECURITY;

-- 2. Create proper RLS policies for production use
-- (Comment out the DISABLE commands above and use these instead)

/*
-- Enable RLS
ALTER TABLE sub_clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE files ENABLE ROW LEVEL SECURITY;
ALTER TABLE outputs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage their own clients' sub-clients
CREATE POLICY "users_manage_sub_clients" ON sub_clients
FOR ALL USING (
  client_id IN (
    SELECT id FROM clients WHERE user_id = auth.uid()::text
  )
);

-- Policy: Users can access files from their clients
CREATE POLICY "users_access_files" ON files
FOR ALL USING (
  user_id = auth.uid()::text OR
  client_id IN (
    SELECT id FROM clients WHERE user_id = auth.uid()::text
  )
);

-- Policy: Users can access outputs from their clients
CREATE POLICY "users_access_outputs" ON outputs
FOR ALL USING (
  user_id = auth.uid()::text OR
  client_id IN (
    SELECT id FROM clients WHERE user_id = auth.uid()::text
  )
);

-- Policy: Sub-client data isolation (sub-clients can only see their own data)
CREATE POLICY "sub_client_isolation" ON files
FOR SELECT USING (
  sub_client_id IS NULL OR
  sub_client_id IN (
    SELECT id FROM sub_clients 
    WHERE client_id IN (
      SELECT id FROM clients WHERE user_id = auth.uid()::text
    )
  )
);
*/

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_clients_user_id ON clients(user_id);
CREATE INDEX IF NOT EXISTS idx_sub_clients_client_id ON sub_clients(client_id);
CREATE INDEX IF NOT EXISTS idx_files_client_id ON files(client_id);
CREATE INDEX IF NOT EXISTS idx_files_user_id ON files(user_id);
CREATE INDEX IF NOT EXISTS idx_outputs_client_id ON outputs(client_id);
CREATE INDEX IF NOT EXISTS idx_outputs_user_id ON outputs(user_id);

-- 4. Add helpful functions for the centralized brain
CREATE OR REPLACE FUNCTION get_client_knowledge(client_uuid UUID)
RETURNS TABLE (
  file_content TEXT,
  file_name TEXT,
  created_at TIMESTAMP,
  output_content TEXT,
  output_type TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    f.extracted_text as file_content,
    f.original_filename as file_name,
    f.created_at,
    o.content as output_content,
    o.output_type
  FROM files f
  FULL OUTER JOIN outputs o ON f.client_id = o.client_id
  WHERE f.client_id = client_uuid OR o.client_id = client_uuid
  ORDER BY COALESCE(f.created_at, o.created_at) DESC;
END;
$$ LANGUAGE plpgsql;

-- 5. Function to get company-wide knowledge for AI context
CREATE OR REPLACE FUNCTION get_company_knowledge(user_uuid UUID)
RETURNS TABLE (
  client_name TEXT,
  project_context TEXT,
  success_patterns TEXT,
  created_at TIMESTAMP
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.name as client_name,
    COALESCE(f.extracted_text, o.content) as project_context,
    CASE 
      WHEN o.output_type = 'proposal' THEN 'Successful Proposal Pattern'
      WHEN o.output_type = 'email' THEN 'Effective Communication Pattern'
      ELSE 'Project Knowledge'
    END as success_patterns,
    COALESCE(f.created_at, o.created_at) as created_at
  FROM clients c
  LEFT JOIN files f ON c.id = f.client_id
  LEFT JOIN outputs o ON c.id = o.client_id
  WHERE c.user_id = user_uuid::text
  AND (f.extracted_text IS NOT NULL OR o.content IS NOT NULL)
  ORDER BY COALESCE(f.created_at, o.created_at) DESC;
END;
$$ LANGUAGE plpgsql;
