#!/bin/bash

# Lemur Multi-Tenant LLM Platform - Development Setup Script
# This script sets up the complete development environment

echo "🚀 Setting up Lemur Multi-Tenant LLM Platform Development Environment..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ and try again."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check for system dependencies
echo "🔍 Checking system dependencies..."

# Check for Tesseract (for OCR)
if ! command -v tesseract &> /dev/null; then
    echo "⚠️  Tesseract OCR not found. Installing..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install tesseract
        else
            echo "❌ Homebrew not found. Please install Tesseract manually:"
            echo "   brew install tesseract"
            echo "   or visit: https://github.com/tesseract-ocr/tesseract"
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        sudo apt-get update && sudo apt-get install -y tesseract-ocr
    else
        echo "❌ Please install Tesseract OCR manually for your system"
        echo "   Visit: https://github.com/tesseract-ocr/tesseract"
    fi
else
    echo "✅ Tesseract OCR found: $(tesseract --version | head -n1)"
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📚 Installing requirements (this may take a few minutes)..."
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p uploads
mkdir -p chroma_db

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your credentials before running the server"
else
    echo "✅ .env file already exists"
fi

# Create submissions.json if it doesn't exist
if [ ! -f "submissions.json" ]; then
    echo "📄 Creating submissions.json file..."
    echo "[]" > submissions.json
else
    echo "✅ submissions.json already exists"
fi

echo ""
echo "🎉 Setup complete! Next steps:"
echo ""
echo "1. Configure your environment variables:"
echo "   nano .env"
echo "   Required: SMTP credentials, Supabase URL/Key, OpenAI API Key"
echo ""
echo "2. Set up Supabase database:"
echo "   - Create a Supabase project"
echo "   - Run the SQL in supabase_setup.sql"
echo "   - Add your Supabase credentials to .env"
echo ""
echo "3. Start all services (RECOMMENDED):"
echo "   ./run_all.sh          # Linux/macOS"
echo "   python launcher.py    # Cross-platform"
echo "   run_all.bat          # Windows"
echo ""
echo "   OR start services individually:"
echo "   source venv/bin/activate"
echo "   python main.py        # API server"
echo "   streamlit run dashboard.py  # Dashboard (in another terminal)"
echo ""
echo "5. Test the API:"
echo "   python test_api.py"
echo ""
echo "6. View API documentation:"
echo "   http://localhost:8000/docs"
echo ""
echo "7. View Dashboard:"
echo "   http://localhost:8501"
echo ""
echo "📖 Documentation:"
echo "   - Email setup: EMAIL_SETUP.md"
echo "   - Development roadmap: MVP_ROADMAP.md"
echo "   - Database setup: supabase_setup.sql"
echo ""
echo "🔧 Features now available:"
echo "   ✅ Phase 1: Waitlist with email notifications"
echo "   ✅ Phase 2: User management with Supabase"
echo "   ✅ Phase 3: File upload and LLM processing"
echo "   ✅ Phase 4: Client dashboard and analytics"
echo ""
