#!/usr/bin/env python3
"""
Simple test to check what's working
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_basic_endpoints():
    """Test basic endpoints that should work"""
    
    print("🧪 Testing Basic Endpoints")
    print("=" * 40)
    
    # Test health
    try:
        response = requests.get(f"{API_BASE}/health")
        print(f"Health Check: {response.status_code}")
        if response.status_code == 200:
            health_data = response.json()
            print(f"  Services: {health_data.get('services', {})}")
            print(f"  Features: {health_data.get('features', {})}")
        else:
            print(f"  Error: {response.text}")
    except Exception as e:
        print(f"Health Check Failed: {e}")
    
    # Test waitlist
    try:
        waitlist_data = {
            "email": "<EMAIL>",
            "name": "Simple Test"
        }
        response = requests.post(f"{API_BASE}/submit", json=waitlist_data)
        print(f"Waitlist: {response.status_code}")
        if response.status_code == 200:
            print(f"  Response: {response.json()}")
        else:
            print(f"  Error: {response.text}")
    except Exception as e:
        print(f"Waitlist Failed: {e}")
    
    # Test submissions
    try:
        response = requests.get(f"{API_BASE}/submissions")
        print(f"Submissions: {response.status_code}")
        if response.status_code == 200:
            submissions = response.json()
            print(f"  Count: {len(submissions)}")
        else:
            print(f"  Error: {response.text}")
    except Exception as e:
        print(f"Submissions Failed: {e}")
    
    # Test dashboard
    try:
        response = requests.get(f"{API_BASE}/dashboard/22222222-2222-2222-2222-222222222222")
        print(f"Dashboard: {response.status_code}")
        if response.status_code == 200:
            print(f"  Response: {response.json()}")
        else:
            print(f"  Error: {response.text}")
    except Exception as e:
        print(f"Dashboard Failed: {e}")

if __name__ == "__main__":
    test_basic_endpoints()
