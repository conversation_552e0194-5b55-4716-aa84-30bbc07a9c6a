#!/usr/bin/env python3
"""
Create users directly in Supabase for multi-client testing
"""

import os
from dotenv import load_dotenv
from supabase import create_client

# Load environment variables
load_dotenv()

def create_test_users():
    """Create test users directly in Supabase"""
    
    # Get Supabase credentials
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_ANON_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Supabase credentials not found in .env file")
        return False
    
    try:
        # Create Supabase client
        supabase = create_client(supabase_url, supabase_key)
        print("✅ Supabase client created successfully")
        
        # Test users to create
        test_users = [
            {
                "email": "<EMAIL>",
                "name": "Tim Cook Test"
            },
            {
                "email": "<EMAIL>", 
                "name": "<PERSON> Test"
            },
            {
                "email": "<EMAIL>",
                "name": "Marta Ortega Test"
            }
        ]
        
        created_users = []
        
        for user_data in test_users:
            try:
                # Check if user already exists
                existing = supabase.table("users").select("*").eq("email", user_data["email"]).execute()
                
                if existing.data:
                    print(f"⚠️  User {user_data['email']} already exists")
                    created_users.append({
                        "email": user_data["email"],
                        "user_id": existing.data[0]["id"],
                        "status": "existing"
                    })
                else:
                    # Create new user
                    result = supabase.table("users").insert(user_data).execute()
                    
                    if result.data:
                        user_id = result.data[0]["id"]
                        print(f"✅ Created user: {user_data['email']} with ID: {user_id}")
                        created_users.append({
                            "email": user_data["email"],
                            "user_id": user_id,
                            "status": "created"
                        })
                    else:
                        print(f"❌ Failed to create user: {user_data['email']}")
                        
            except Exception as e:
                print(f"❌ Error creating user {user_data['email']}: {e}")
        
        # Print summary
        print("\n" + "="*50)
        print("USER CREATION SUMMARY")
        print("="*50)
        
        for user in created_users:
            status_icon = "✅" if user["status"] in ["created", "existing"] else "❌"
            print(f"{status_icon} {user['email']}")
            print(f"   User ID: {user['user_id']}")
            print(f"   Status: {user['status']}")
            print()
        
        # Generate curl commands for testing
        print("🚀 CURL COMMANDS FOR TESTING:")
        print("="*50)
        
        if created_users:
            apple_user = next((u for u in created_users if "apple" in u["email"]), None)
            nike_user = next((u for u in created_users if "nike" in u["email"]), None)
            zara_user = next((u for u in created_users if "zara" in u["email"]), None)
            
            if apple_user:
                print(f"# Apple User ID: {apple_user['user_id']}")
                print(f"APPLE_USER_ID=\"{apple_user['user_id']}\"")
                print()
                
            if nike_user:
                print(f"# Nike User ID: {nike_user['user_id']}")
                print(f"NIKE_USER_ID=\"{nike_user['user_id']}\"")
                print()
                
            if zara_user:
                print(f"# Zara User ID: {zara_user['user_id']}")
                print(f"ZARA_USER_ID=\"{zara_user['user_id']}\"")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error connecting to Supabase: {e}")
        return False

def test_user_creation_api():
    """Test user creation through the API"""
    
    import requests
    
    print("\n🧪 Testing API User Creation")
    print("="*30)
    
    test_users = [
        {
            "email": "<EMAIL>",
            "name": "API Test Apple",
            "password": "testpass123"
        },
        {
            "email": "<EMAIL>",
            "name": "API Test Nike", 
            "password": "testpass123"
        },
        {
            "email": "<EMAIL>",
            "name": "API Test Zara",
            "password": "testpass123"
        }
    ]
    
    api_base = "http://localhost:8000"
    
    for user_data in test_users:
        try:
            response = requests.post(
                f"{api_base}/users/register",
                json=user_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API created user: {user_data['email']}")
                print(f"   User ID: {result.get('user_id', 'N/A')}")
            else:
                print(f"❌ API failed for {user_data['email']}: {response.text}")
                
        except Exception as e:
            print(f"❌ API error for {user_data['email']}: {e}")

if __name__ == "__main__":
    print("🧪 Creating Test Users for Multi-Client Testing")
    print("=" * 60)
    
    # Create users directly in Supabase
    success = create_test_users()
    
    if success:
        # Test API user creation
        test_user_creation_api()
        
        print("\n🎉 User creation complete!")
        print("You can now use the User IDs above for multi-client testing.")
    else:
        print("\n❌ User creation failed. Check your Supabase configuration.")
