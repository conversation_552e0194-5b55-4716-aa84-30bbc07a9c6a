"""
Database configuration and models for Supabase integration
"""

import os
from typing import Optional, List, Dict, Any
from datetime import datetime
from supabase import create_client, Client
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY", "")

# Initialize Supabase client
supabase: Optional[Client] = None

def init_supabase():
    """Initialize Supabase client"""
    global supabase
    if SUPABASE_URL and SUPABASE_KEY:
        try:
            supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
            logger.info("Supabase client initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            return False
    else:
        logger.warning("Supabase credentials not configured")
        return False

def get_supabase() -> Optional[Client]:
    """Get Supabase client instance"""
    if supabase is None:
        init_supabase()
    return supabase

# Pydantic models for database entities

class User(BaseModel):
    id: Optional[str] = None
    email: str
    name: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class Client(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    user_id: str  # Owner of the client organization
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True

class SubClient(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    client_id: str  # Parent client
    contact_email: Optional[str] = None
    contact_name: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True

class File(BaseModel):
    id: Optional[str] = None
    filename: str
    original_filename: str
    file_type: str
    file_size: int
    storage_path: str
    client_id: str
    sub_client_id: Optional[str] = None
    user_id: str
    processed: bool = False
    extracted_text: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class Meeting(BaseModel):
    id: Optional[str] = None
    title: str
    description: Optional[str] = None
    transcript: Optional[str] = None
    client_id: str
    sub_client_id: Optional[str] = None
    user_id: str
    meeting_date: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class Output(BaseModel):
    id: Optional[str] = None
    title: str
    content: str
    output_type: str  # email, summary, action_items, etc.
    prompt: Optional[str] = None
    client_id: str
    sub_client_id: Optional[str] = None
    user_id: str
    file_id: Optional[str] = None
    meeting_id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# Database operations

class DatabaseOperations:
    """Database operations for all entities"""
    
    def __init__(self):
        self.client = get_supabase()
    
    # User operations
    async def create_user(self, user: User) -> Optional[Dict[str, Any]]:
        """Create a new user"""
        if not self.client:
            return None

        try:
            # Manually serialize the user data to handle datetime objects
            user_data = {
                "email": user.email,
                "name": user.name,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
                "is_active": user.is_active
            }
            # Remove None values
            user_data = {k: v for k, v in user_data.items() if v is not None}

            result = self.client.table("users").insert(user_data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email"""
        if not self.client:
            return None
        
        try:
            result = self.client.table("users").select("*").eq("email", email).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting user by email: {e}")
            return None
    
    # Client operations
    async def create_client(self, client: Client) -> Optional[Dict[str, Any]]:
        """Create a new client organization"""
        if not self.client:
            return None

        try:
            # Manually serialize the client data to handle datetime objects
            client_data = {
                "name": client.name,
                "description": client.description,
                "user_id": client.user_id,
                "created_at": client.created_at.isoformat() if client.created_at else None,
                "updated_at": client.updated_at.isoformat() if client.updated_at else None,
                "is_active": client.is_active
            }
            # Remove None values
            client_data = {k: v for k, v in client_data.items() if v is not None}

            result = self.client.table("clients").insert(client_data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating client: {e}")
            return None
    
    async def get_clients_by_user(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all clients for a user"""
        if not self.client:
            return []
        
        try:
            result = self.client.table("clients").select("*").eq("user_id", user_id).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error getting clients: {e}")
            return []
    
    # Sub-client operations
    async def create_sub_client(self, sub_client: SubClient) -> Optional[Dict[str, Any]]:
        """Create a new sub-client"""
        if not self.client:
            return None
        
        try:
            result = self.client.table("sub_clients").insert(sub_client.dict(exclude_none=True)).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating sub-client: {e}")
            return None
    
    async def get_sub_clients_by_client(self, client_id: str) -> List[Dict[str, Any]]:
        """Get all sub-clients for a client"""
        if not self.client:
            return []
        
        try:
            result = self.client.table("sub_clients").select("*").eq("client_id", client_id).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error getting sub-clients: {e}")
            return []
    
    # File operations
    async def create_file_record(self, file: File) -> Optional[Dict[str, Any]]:
        """Create a file record"""
        if not self.client:
            return None
        
        try:
            result = self.client.table("files").insert(file.dict(exclude_none=True)).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating file record: {e}")
            return None
    
    async def get_files_by_client(self, client_id: str, sub_client_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get files for a client/sub-client"""
        if not self.client:
            return []
        
        try:
            query = self.client.table("files").select("*").eq("client_id", client_id)
            if sub_client_id:
                query = query.eq("sub_client_id", sub_client_id)
            result = query.execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error getting files: {e}")
            return []
    
    # Output operations
    async def create_output(self, output: Output) -> Optional[Dict[str, Any]]:
        """Create an LLM output record"""
        if not self.client:
            return None
        
        try:
            result = self.client.table("outputs").insert(output.dict(exclude_none=True)).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating output: {e}")
            return None
    
    async def get_outputs_by_client(self, client_id: str, sub_client_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get outputs for a client/sub-client"""
        if not self.client:
            return []
        
        try:
            query = self.client.table("outputs").select("*").eq("client_id", client_id)
            if sub_client_id:
                query = query.eq("sub_client_id", sub_client_id)
            result = query.execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error getting outputs: {e}")
            return []

# Global database instance
db = DatabaseOperations()
