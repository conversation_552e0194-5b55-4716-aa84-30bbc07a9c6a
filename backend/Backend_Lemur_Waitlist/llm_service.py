"""
LLM service for generating content using OpenAI GPT models
"""

import os
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

import openai
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import PromptTemplate

from file_processor import search_knowledge_base

logger = logging.getLogger(__name__)

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
DEFAULT_MODEL = "gpt-4"
DEFAULT_TEMPERATURE = 0.7

# Initialize OpenAI
if OPENAI_API_KEY:
    openai.api_key = OPENAI_API_KEY

class LLMService:
    """Service for LLM-powered content generation"""
    
    def __init__(self, model: str = DEFAULT_MODEL, temperature: float = DEFAULT_TEMPERATURE):
        self.model = model
        self.temperature = temperature
        self.llm = ChatOpenAI(
            model_name=model,
            temperature=temperature,
            openai_api_key=OPENAI_API_KEY
        ) if OPENAI_API_KEY else None
    
    def _get_context_from_knowledge_base(self, 
                                       query: str, 
                                       client_id: str, 
                                       sub_client_id: Optional[str] = None) -> str:
        """Retrieve relevant context from the knowledge base"""
        try:
            results = search_knowledge_base(query, client_id, sub_client_id, n_results=5)
            
            if not results:
                return "No relevant information found in the knowledge base."
            
            context_parts = []
            for i, result in enumerate(results, 1):
                context_parts.append(f"Context {i}:\n{result['text']}\n")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error retrieving context: {e}")
            return "Error retrieving context from knowledge base."
    
    async def generate_email(self, 
                           prompt: str, 
                           client_id: str, 
                           sub_client_id: Optional[str] = None,
                           recipient_name: Optional[str] = None,
                           sender_name: Optional[str] = None) -> Dict[str, Any]:
        """Generate a professional email"""
        
        if not self.llm:
            return {"success": False, "error": "OpenAI API key not configured"}
        
        try:
            # Get relevant context
            context = self._get_context_from_knowledge_base(prompt, client_id, sub_client_id)
            
            # Create email generation prompt
            email_template = PromptTemplate(
                input_variables=["context", "prompt", "recipient_name", "sender_name"],
                template="""
You are a professional email writer. Based on the following context and request, write a clear, professional email.

Context from knowledge base:
{context}

Request: {prompt}

Recipient: {recipient_name}
Sender: {sender_name}

Please write a professional email that:
1. Has an appropriate subject line
2. Is well-structured and clear
3. Uses the context information when relevant
4. Maintains a professional tone
5. Includes a proper greeting and closing

Format your response as:
Subject: [subject line]

[email body]

Best regards,
{sender_name}
"""
            )
            
            formatted_prompt = email_template.format(
                context=context,
                prompt=prompt,
                recipient_name=recipient_name or "Valued Client",
                sender_name=sender_name or "Your Team"
            )
            
            # Generate email
            messages = [
                SystemMessage(content="You are a professional email writer."),
                HumanMessage(content=formatted_prompt)
            ]
            
            response = self.llm(messages)
            
            return {
                "success": True,
                "content": response.content,
                "type": "email",
                "context_used": context,
                "prompt": prompt
            }
            
        except Exception as e:
            logger.error(f"Error generating email: {e}")
            return {"success": False, "error": str(e)}
    
    async def generate_summary(self, 
                             prompt: str, 
                             client_id: str, 
                             sub_client_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate a summary based on the knowledge base"""
        
        if not self.llm:
            return {"success": False, "error": "OpenAI API key not configured"}
        
        try:
            # Get relevant context
            context = self._get_context_from_knowledge_base(prompt, client_id, sub_client_id)
            
            # Create summary prompt
            summary_template = PromptTemplate(
                input_variables=["context", "prompt"],
                template="""
Based on the following information, create a comprehensive summary.

Context:
{context}

Request: {prompt}

Please provide a well-structured summary that:
1. Captures the key points
2. Is organized and easy to read
3. Highlights important insights
4. Uses bullet points or sections where appropriate
"""
            )
            
            formatted_prompt = summary_template.format(
                context=context,
                prompt=prompt
            )
            
            # Generate summary
            messages = [
                SystemMessage(content="You are an expert at creating clear, comprehensive summaries."),
                HumanMessage(content=formatted_prompt)
            ]
            
            response = self.llm(messages)
            
            return {
                "success": True,
                "content": response.content,
                "type": "summary",
                "context_used": context,
                "prompt": prompt
            }
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return {"success": False, "error": str(e)}
    
    async def generate_action_items(self, 
                                  prompt: str, 
                                  client_id: str, 
                                  sub_client_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate action items based on the knowledge base"""
        
        if not self.llm:
            return {"success": False, "error": "OpenAI API key not configured"}
        
        try:
            # Get relevant context
            context = self._get_context_from_knowledge_base(prompt, client_id, sub_client_id)
            
            # Create action items prompt
            action_template = PromptTemplate(
                input_variables=["context", "prompt"],
                template="""
Based on the following information, create a list of actionable items.

Context:
{context}

Request: {prompt}

Please provide a clear list of action items that:
1. Are specific and actionable
2. Include responsible parties when possible
3. Have suggested timelines or priorities
4. Are organized by importance or category
5. Use clear, concise language

Format as a numbered list with details for each item.
"""
            )
            
            formatted_prompt = action_template.format(
                context=context,
                prompt=prompt
            )
            
            # Generate action items
            messages = [
                SystemMessage(content="You are an expert at creating actionable task lists and project plans."),
                HumanMessage(content=formatted_prompt)
            ]
            
            response = self.llm(messages)
            
            return {
                "success": True,
                "content": response.content,
                "type": "action_items",
                "context_used": context,
                "prompt": prompt
            }
            
        except Exception as e:
            logger.error(f"Error generating action items: {e}")
            return {"success": False, "error": str(e)}
    
    async def generate_custom_content(self, 
                                    prompt: str, 
                                    content_type: str,
                                    client_id: str, 
                                    sub_client_id: Optional[str] = None,
                                    additional_instructions: Optional[str] = None) -> Dict[str, Any]:
        """Generate custom content based on user requirements"""
        
        if not self.llm:
            return {"success": False, "error": "OpenAI API key not configured"}
        
        try:
            # Get relevant context
            context = self._get_context_from_knowledge_base(prompt, client_id, sub_client_id)
            
            # Create custom prompt
            custom_template = PromptTemplate(
                input_variables=["context", "prompt", "content_type", "additional_instructions"],
                template="""
Based on the following information, create {content_type} content.

Context:
{context}

Request: {prompt}

Additional Instructions: {additional_instructions}

Please create high-quality {content_type} that:
1. Uses the provided context appropriately
2. Meets the specific requirements in the request
3. Follows best practices for {content_type}
4. Is well-structured and professional
"""
            )
            
            formatted_prompt = custom_template.format(
                context=context,
                prompt=prompt,
                content_type=content_type,
                additional_instructions=additional_instructions or "None"
            )
            
            # Generate content
            messages = [
                SystemMessage(content=f"You are an expert at creating {content_type} content."),
                HumanMessage(content=formatted_prompt)
            ]
            
            response = self.llm(messages)
            
            return {
                "success": True,
                "content": response.content,
                "type": content_type,
                "context_used": context,
                "prompt": prompt
            }
            
        except Exception as e:
            logger.error(f"Error generating custom content: {e}")
            return {"success": False, "error": str(e)}

# Global LLM service instance
llm_service = LLMService()

# Convenience functions
async def generate_email(prompt: str, client_id: str, sub_client_id: Optional[str] = None, **kwargs):
    """Generate an email"""
    return await llm_service.generate_email(prompt, client_id, sub_client_id, **kwargs)

async def generate_summary(prompt: str, client_id: str, sub_client_id: Optional[str] = None):
    """Generate a summary"""
    return await llm_service.generate_summary(prompt, client_id, sub_client_id)

async def generate_action_items(prompt: str, client_id: str, sub_client_id: Optional[str] = None):
    """Generate action items"""
    return await llm_service.generate_action_items(prompt, client_id, sub_client_id)

async def generate_custom_content(prompt: str, content_type: str, client_id: str, sub_client_id: Optional[str] = None, **kwargs):
    """Generate custom content"""
    return await llm_service.generate_custom_content(prompt, content_type, client_id, sub_client_id, **kwargs)
