services:
  - type: web
    name: waitlist-api
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: ENVIRONMENT
        value: production
      - key: ALLOWED_ORIGINS
        value: "https://your-frontend-domain.com,http://localhost:3000"
      - key: PORT
        fromService:
          type: web
          name: waitlist-api
          envVarKey: PORT
      # Email configuration - Set these in Render dashboard
      - key: SMTP_SERVER
        value: smtp.gmail.com
      - key: SMTP_PORT
        value: 587
      - key: SMTP_USERNAME
        value: ""  # Set this in Render dashboard
      - key: SMTP_PASSWORD
        value: ""  # Set this in Render dashboard (use App Password for Gmail)
      - key: FROM_EMAIL
        value: ""  # Set this in Render dashboard (usually same as SMTP_USERNAME)
      - key: FROM_NAME
        value: "Lemur Waitlist"
