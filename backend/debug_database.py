#!/usr/bin/env python3
"""
Debug database connectivity and table structure
"""

import os
import sys
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Add Backend_Lemur_Waitlist to path
sys.path.append('./Backend_Lemur_Waitlist')

print("🔍 Debugging database issues...")
print("=" * 50)

# Test Supabase connection
try:
    from Backend_Lemur_Waitlist.database import get_supabase, DatabaseOperations, Client as DBClient
    
    print("1. Testing Supabase connection...")
    supabase = get_supabase()
    if supabase:
        print("✅ Supabase client connected")
    else:
        print("❌ Supabase client failed")
        exit(1)
    
    print("\n2. Testing database operations...")
    db = DatabaseOperations()
    
    # Test simple query to check if tables exist
    print("   Testing table access...")
    try:
        # Try to query the clients table
        result = supabase.table("clients").select("*").limit(1).execute()
        print(f"✅ Clients table accessible, found {len(result.data)} records")
    except Exception as e:
        print(f"❌ Clients table error: {e}")
        
        # Check if table exists
        try:
            # Try to get table info
            tables_result = supabase.rpc('get_table_info', {'table_name': 'clients'}).execute()
            print(f"   Table info: {tables_result.data}")
        except Exception as e2:
            print(f"   Cannot get table info: {e2}")
    
    print("\n3. Testing client creation manually...")
    try:
        # Create a test client manually
        test_client = DBClient(
            name="Debug Test Client",
            description="Testing database connectivity",
            user_id="test_user_123",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        print(f"   Client object created: {test_client.name}")

        # Try to insert directly with supabase client
        client_data = {
            "name": test_client.name,
            "description": test_client.description,
            "user_id": test_client.user_id,
            "created_at": test_client.created_at.isoformat(),
            "updated_at": test_client.updated_at.isoformat(),
            "is_active": test_client.is_active
        }

        result = supabase.table("clients").insert(client_data).execute()
        if result.data:
            print(f"✅ Client created successfully: {result.data[0]}")
        else:
            print("❌ Client creation returned no data")

    except Exception as e:
        print(f"❌ Client creation failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n4. Checking table schema...")
    try:
        # Get table columns
        schema_result = supabase.rpc('get_table_schema', {'table_name': 'clients'}).execute()
        print(f"   Table schema: {schema_result.data}")
    except Exception as e:
        print(f"   Cannot get schema: {e}")
        
        # Alternative: try to describe the table structure
        try:
            # Try a different approach to check table structure
            result = supabase.table("clients").select("*").limit(0).execute()
            print("   Table exists but may be empty")
        except Exception as e2:
            print(f"   Table access error: {e2}")

except Exception as e:
    print(f"❌ Database debug failed: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("🏁 Database debug complete!")
