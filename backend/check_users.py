#!/usr/bin/env python3
"""
Check if users exist in the Supabase database
"""

import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add Backend_Lemur_Waitlist to path
sys.path.append('./Backend_Lemur_Waitlist')

def check_users():
    print("🔍 Checking users in Supabase database...")
    
    try:
        from Backend_Lemur_Waitlist.database import get_supabase
        
        supabase = get_supabase()
        if not supabase:
            print("❌ Supabase connection failed")
            return
        
        print("✅ Supabase connection successful")
        
        # Check all users in the database
        result = supabase.table("users").select("*").execute()
        
        print(f"\n📊 Users in database: {len(result.data)}")
        
        if result.data:
            for user in result.data:
                print(f"   - {user['email']} (ID: {user['id']})")
        else:
            print("   No users found in database!")
        
        # Check for specific demo users
        demo_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        
        print(f"\n🔍 Checking for demo users...")
        for email in demo_emails:
            user_result = supabase.table("users").select("*").eq("email", email).execute()
            if user_result.data:
                user = user_result.data[0]
                print(f"   ✅ {email} exists (ID: {user['id']})")
            else:
                print(f"   ❌ {email} NOT found")
        
        # Try to create a demo user manually
        print(f"\n🛠️  Attempting to create demo user manually...")
        
        demo_user = {
            "id": "550e8400-e29b-41d4-a716-446655440002",
            "email": "<EMAIL>",
            "name": "Aditi Sirigineedi",
            "created_at": "2025-06-09T18:00:00.000000",
            "updated_at": "2025-06-09T18:00:00.000000",
            "is_active": True
        }
        
        # Check if user already exists
        existing = supabase.table("users").select("*").eq("email", demo_user["email"]).execute()
        if existing.data:
            print(f"   ✅ User {demo_user['email']} already exists")
        else:
            # Create the user
            create_result = supabase.table("users").insert(demo_user).execute()
            if create_result.data:
                print(f"   ✅ User {demo_user['email']} created successfully!")
                print(f"      ID: {create_result.data[0]['id']}")
            else:
                print(f"   ❌ Failed to create user {demo_user['email']}")
                
    except Exception as e:
        print(f"❌ Error checking users: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_users()
