#!/usr/bin/env python3
"""
Diagnostic script to check what's preventing the server from starting
"""

import os
import sys
from dotenv import load_dotenv

print("🔍 Diagnosing backend startup issues...")
print("=" * 50)

# 1. Check if .env file exists and loads
print("1. Checking .env file...")
if os.path.exists('.env'):
    print("✅ .env file exists")
    load_dotenv()
    print("✅ .env file loaded")
else:
    print("❌ .env file not found")

# 2. Check critical environment variables
print("\n2. Checking environment variables...")
env_vars = {
    'RECALL_API_KEY': os.getenv('RECALL_API_KEY'),
    'SUPABASE_URL': os.getenv('SUPABASE_URL'),
    'SUPABASE_ANON_KEY': os.getenv('SUPABASE_ANON_KEY'),
    'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
    'JWT_SECRET_KEY': os.getenv('JWT_SECRET_KEY')
}

for var, value in env_vars.items():
    if value:
        print(f"✅ {var}: {'*' * 10}...{value[-10:] if len(value) > 10 else value}")
    else:
        print(f"❌ {var}: Not set")

# 3. Check if Backend_Lemur_Waitlist directory exists
print("\n3. Checking ML Pipeline directory...")
if os.path.exists('Backend_Lemur_Waitlist'):
    print("✅ Backend_Lemur_Waitlist directory exists")
    files = os.listdir('Backend_Lemur_Waitlist')
    required_files = ['database.py', 'file_processor.py', 'llm_service.py']
    for file in required_files:
        if file in files:
            print(f"✅ {file} found")
        else:
            print(f"❌ {file} missing")
else:
    print("❌ Backend_Lemur_Waitlist directory not found")

# 4. Test basic imports
print("\n4. Testing basic imports...")
try:
    import fastapi
    print("✅ FastAPI imported")
except ImportError as e:
    print(f"❌ FastAPI import failed: {e}")

try:
    import uvicorn
    print("✅ Uvicorn imported")
except ImportError as e:
    print(f"❌ Uvicorn import failed: {e}")

try:
    import pydantic
    print("✅ Pydantic imported")
except ImportError as e:
    print(f"❌ Pydantic import failed: {e}")

# 5. Test ML Pipeline imports
print("\n5. Testing ML Pipeline imports...")
sys.path.append('./Backend_Lemur_Waitlist')

try:
    from Backend_Lemur_Waitlist.database import DatabaseOperations
    print("✅ Database module imported")
except ImportError as e:
    print(f"❌ Database import failed: {e}")

try:
    from Backend_Lemur_Waitlist.file_processor import process_and_store_file
    print("✅ File processor imported")
except ImportError as e:
    print(f"❌ File processor import failed: {e}")

try:
    from Backend_Lemur_Waitlist.llm_service import generate_email
    print("✅ LLM service imported")
except ImportError as e:
    print(f"❌ LLM service import failed: {e}")

# 6. Test database connection
print("\n6. Testing database connection...")
try:
    from Backend_Lemur_Waitlist.database import init_supabase
    result = init_supabase()
    if result:
        print("✅ Supabase connection successful")
    else:
        print("❌ Supabase connection failed")
except Exception as e:
    print(f"❌ Database connection error: {e}")

# 7. Try importing main.py
print("\n7. Testing main.py import...")
try:
    import main
    print("✅ main.py imported successfully")
    print(f"✅ ML_PIPELINE_AVAILABLE: {getattr(main, 'ML_PIPELINE_AVAILABLE', 'Not found')}")
except Exception as e:
    print(f"❌ main.py import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("🏁 Diagnosis complete!")
