#!/usr/bin/env python3
"""
ML Pipeline Test Script
Tests all ML pipeline functionality including database, client management, and content generation
"""

import requests
import json
import time
import os
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "synatech@Aditi"

class MLPipelineTest:
    def __init__(self):
        self.base_url = BASE_URL
        self.token = None
        self.user_id = None
        self.client_id = None
        self.sub_client_id = None
        
    def log(self, message):
        """Log test messages with timestamp"""
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        
    def login(self):
        """Login and get authentication token"""
        self.log("🔐 Testing user authentication...")
        
        login_data = {
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }
        
        response = requests.post(f"{self.base_url}/auth/login", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            self.token = data["access_token"]
            self.user_id = data["user"]["id"]
            self.log(f"✅ Login successful! User ID: {self.user_id}")
            return True
        else:
            self.log(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
    
    def get_headers(self):
        """Get authorization headers"""
        return {"Authorization": f"Bearer {self.token}"}
    
    def test_health_check(self):
        """Test basic health check"""
        self.log("🏥 Testing health check...")
        
        response = requests.get(f"{self.base_url}/health")
        
        if response.status_code == 200:
            self.log("✅ Health check passed")
            return True
        else:
            self.log(f"❌ Health check failed: {response.status_code}")
            return False
    
    def test_create_client(self):
        """Test creating a client organization"""
        self.log("🏢 Testing client creation...")
        
        client_data = {
            "name": "Test Corporation",
            "description": "A test client organization for ML pipeline testing"
        }
        
        response = requests.post(
            f"{self.base_url}/clients",
            json=client_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            data = response.json()
            self.client_id = data["id"]
            self.log(f"✅ Client created successfully! Client ID: {self.client_id}")
            self.log(f"   Name: {data['name']}")
            self.log(f"   Description: {data['description']}")
            return True
        else:
            self.log(f"❌ Client creation failed: {response.status_code} - {response.text}")
            return False
    
    def test_get_clients(self):
        """Test retrieving user's clients"""
        self.log("📋 Testing client retrieval...")
        
        response = requests.get(
            f"{self.base_url}/clients",
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            clients = response.json()
            self.log(f"✅ Retrieved {len(clients)} clients")
            for client in clients:
                self.log(f"   - {client['name']} (ID: {client['id']})")
            return True
        else:
            self.log(f"❌ Client retrieval failed: {response.status_code} - {response.text}")
            return False
    
    def test_create_sub_client(self):
        """Test creating a sub-client"""
        if not self.client_id:
            self.log("❌ Cannot test sub-client creation: No client ID available")
            return False
            
        self.log("🏢 Testing sub-client creation...")
        
        sub_client_data = {
            "name": "Marketing Department",
            "description": "Marketing team sub-client",
            "client_id": self.client_id,
            "contact_email": "<EMAIL>",
            "contact_name": "Marketing Manager"
        }
        
        response = requests.post(
            f"{self.base_url}/clients/{self.client_id}/sub-clients",
            json=sub_client_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            data = response.json()
            self.sub_client_id = data["id"]
            self.log(f"✅ Sub-client created successfully! Sub-client ID: {self.sub_client_id}")
            self.log(f"   Name: {data['name']}")
            self.log(f"   Contact: {data['contact_name']} ({data['contact_email']})")
            return True
        else:
            self.log(f"❌ Sub-client creation failed: {response.status_code} - {response.text}")
            return False
    
    def test_get_sub_clients(self):
        """Test retrieving sub-clients"""
        if not self.client_id:
            self.log("❌ Cannot test sub-client retrieval: No client ID available")
            return False
            
        self.log("📋 Testing sub-client retrieval...")
        
        response = requests.get(
            f"{self.base_url}/clients/{self.client_id}/sub-clients",
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            sub_clients = response.json()
            self.log(f"✅ Retrieved {len(sub_clients)} sub-clients")
            for sub_client in sub_clients:
                self.log(f"   - {sub_client['name']} (ID: {sub_client['id']})")
            return True
        else:
            self.log(f"❌ Sub-client retrieval failed: {response.status_code} - {response.text}")
            return False
    
    def test_file_upload(self):
        """Test file upload and processing"""
        if not self.client_id:
            self.log("❌ Cannot test file upload: No client ID available")
            return False
            
        self.log("📄 Testing file upload and processing...")
        
        # Create a test text file
        test_content = """
        This is a test document for the ML pipeline.
        
        Project Overview:
        - Client: Test Corporation
        - Department: Marketing
        - Goal: Test document processing and AI content generation
        
        Key Points:
        1. Document upload functionality
        2. Text extraction and processing
        3. Vector embedding creation
        4. Knowledge base storage
        
        This document should be processed and made available for AI content generation.
        """
        
        files = {
            'file': ('test_document.txt', test_content, 'text/plain')
        }
        
        data = {
            'sub_client_id': self.sub_client_id if self.sub_client_id else ''
        }
        
        response = requests.post(
            f"{self.base_url}/clients/{self.client_id}/files",
            files=files,
            data=data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            file_data = response.json()
            self.log(f"✅ File uploaded and processed successfully!")
            self.log(f"   File ID: {file_data['id']}")
            self.log(f"   Original name: {file_data['original_filename']}")
            self.log(f"   Processed: {file_data['processed']}")
            self.log(f"   Chunks stored: {file_data['chunks_stored']}")
            self.log(f"   Extracted text preview: {file_data['extracted_text'][:100]}...")
            return True
        else:
            self.log(f"❌ File upload failed: {response.status_code} - {response.text}")
            return False
    
    def test_knowledge_search(self):
        """Test knowledge base search"""
        if not self.client_id:
            self.log("❌ Cannot test knowledge search: No client ID available")
            return False
            
        self.log("🔍 Testing knowledge base search...")
        
        search_data = {
            "query": "project overview and goals",
            "client_id": self.client_id,
            "sub_client_id": self.sub_client_id,
            "n_results": 3
        }
        
        response = requests.post(
            f"{self.base_url}/knowledge/search",
            json=search_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            search_results = response.json()
            self.log(f"✅ Knowledge search successful!")
            self.log(f"   Query: {search_results['query']}")
            self.log(f"   Total results: {search_results['total_results']}")
            
            for i, result in enumerate(search_results['results'][:2]):
                self.log(f"   Result {i+1}: {result['text'][:100]}...")
            return True
        else:
            self.log(f"❌ Knowledge search failed: {response.status_code} - {response.text}")
            return False
    
    def test_ai_content_generation(self):
        """Test AI content generation"""
        if not self.client_id:
            self.log("❌ Cannot test AI generation: No client ID available")
            return False
            
        self.log("🤖 Testing AI content generation...")
        
        # Test summary generation
        generation_data = {
            "prompt": "Create a summary of the project information and key goals",
            "content_type": "summary",
            "client_id": self.client_id,
            "sub_client_id": self.sub_client_id
        }
        
        response = requests.post(
            f"{self.base_url}/llm/generate",
            json=generation_data,
            headers=self.get_headers()
        )
        
        if response.status_code == 200:
            generation_result = response.json()
            self.log(f"✅ AI content generation successful!")
            self.log(f"   Content type: {generation_result['content_type']}")
            self.log(f"   Generated content preview: {generation_result['content'][:200]}...")
            return True
        else:
            self.log(f"❌ AI content generation failed: {response.status_code} - {response.text}")
            return False
    
    def run_all_tests(self):
        """Run all ML pipeline tests"""
        self.log("🚀 Starting ML Pipeline Tests")
        self.log("=" * 50)
        
        tests = [
            ("Health Check", self.test_health_check),
            ("User Authentication", self.login),
            ("Client Creation", self.test_create_client),
            ("Client Retrieval", self.test_get_clients),
            ("Sub-Client Creation", self.test_create_sub_client),
            ("Sub-Client Retrieval", self.test_get_sub_clients),
            ("File Upload & Processing", self.test_file_upload),
            ("Knowledge Base Search", self.test_knowledge_search),
            ("AI Content Generation", self.test_ai_content_generation)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            self.log(f"\n📋 Running: {test_name}")
            try:
                if test_func():
                    passed += 1
                time.sleep(1)  # Brief pause between tests
            except Exception as e:
                self.log(f"❌ {test_name} failed with exception: {str(e)}")
        
        self.log("\n" + "=" * 50)
        self.log(f"🏁 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            self.log("🎉 All ML Pipeline tests passed!")
        else:
            self.log(f"⚠️  {total - passed} tests failed")
        
        return passed == total

if __name__ == "__main__":
    tester = MLPipelineTest()
    success = tester.run_all_tests()
    exit(0 if success else 1)
