# Multi-Tenant LLM-Powered B2B Tool: MVP Setup Guide (4-Hour Sprint)

This document explains how to build a **minimal viable product (<PERSON>)** version of a multi-tenant LLM platform in under 4 hours. You'll enable file uploads, meeting transcripts, and LLM-generated outputs (emails, summaries, action items) with full tenant isolation.

---

## ✅ MVP GOAL: End-to-End Flow

1. Upload files (PDF, DOCX, Images)
2. Auto-extract content (OCR + parsing)
3. Store as vector embeddings
4. Generate emails/action items from LLM

---

## 🛠️ Tech Stack (Fast Setup)

| Layer        | Tool                                |
| ------------ | ----------------------------------- |
| Backend API  | **FastAPI** (with Uvicorn)          |
| Auth & DB    | **Supabase**                        |
| File Storage | **Supabase Storage**                |
| Embeddings   | **OpenAI `text-embedding-3-small`** |
| Vector Store | **OpenAI Vector Store**             |
| LLM          | **OpenAI GPT-4**                    |
| Frontend     | **Streamlit** (quick UI)            |

---

## 🔧 Setup Steps

### 1. Supabase

* Create a project
* Enable Auth (email/password)
* Add tables:

  * `users` – your platform users
  * `clients` – your B2B customer orgs
  * `sub_clients` – your client's clients
  * `files` – uploaded files
  * `meetings` – meeting transcripts
  * `outputs` – LLM generated responses
* Setup RLS on all tables by `user_id` or `client_id`

### 2. Backend: FastAPI

```bash
pip install fastapi uvicorn openai langchain python-multipart
```

### File Upload + Parsing

```python
@app.post("/upload/")
async def upload(file: UploadFile, client_id: str, sub_client_id: str):
    contents = await file.read()
    parsed_text = parse_file(contents)
    chunks = split_chunks(parsed_text)
    store_embeddings(chunks, client_id, sub_client_id)
    return {"msg": "Uploaded & embedded"}
```

### Basic File Parsing

```python
from unstructured.partition.pdf import partition_pdf

def parse_file(content):
    with open("temp.pdf", "wb") as f: f.write(content)
    return "\n".join([el.text for el in partition_pdf(filename="temp.pdf")])
```

### Store in OpenAI Vector Store

```python
from langchain.vectorstores import OpenAIVectorStore
from langchain.embeddings.openai import OpenAIEmbeddings

embedding = OpenAIEmbeddings()

vectorstore = OpenAIVectorStore(
    embedding=embedding,
    project="your-openai-project-name"
)

def store_embeddings(chunks, client_id, sub_client_id):
    metadatas = [{"client_id": client_id, "sub_client_id": sub_client_id}] * len(chunks)
    vectorstore.add_texts(texts=chunks, metadatas=metadatas)
```

---

## 🤖 LLM-Based Retrieval & Generation

### Prompt Generation

```python
from langchain.chat_models import ChatOpenAI
from langchain.chains import RetrievalQA

def generate_output(prompt, client_id, sub_client_id):
    retriever = vectorstore.as_retriever(
        search_kwargs={"filter": {"client_id": client_id, "sub_client_id": sub_client_id}}
    )
    qa = RetrievalQA.from_chain_type(
        llm=ChatOpenAI(model_name="gpt-4"),
        retriever=retriever
    )
    return qa.run(prompt)
```

---

## 🖥️ Frontend: Streamlit MVP

```python
import streamlit as st
import requests

st.title("Client Assistant")
client_id = st.text_input("Client ID")
sub_client_id = st.text_input("Sub-Client ID")
file = st.file_uploader("Upload File")

if file:
    res = requests.post(
        "http://localhost:8000/upload/",
        files={"file": file},
        data={"client_id": client_id, "sub_client_id": sub_client_id}
    )
    st.success(res.json()["msg"])

prompt = st.text_input("Ask something")
if st.button("Generate Action Items"):
    res = requests.post("http://localhost:8000/generate/", json={"prompt": prompt, "client_id": client_id, "sub_client_id": sub_client_id})
    st.write(res.json()["response"])
```

---

## 🚀 Local Run (Dev)

1. Start FastAPI:

```bash
uvicorn app:app --reload
```

2. Run Streamlit UI:

```bash
streamlit run ui.py
```

---

## 🔐 Data Isolation MVP

* Each embedding is stored with `client_id` and `sub_client_id` metadata
* Retrieval filters ensure scoped outputs
* Supabase auth + RLS ensures DB and file-level access control

---

## 🌐 Central Dashboard for Clients

* Use Supabase SQL views or materialized views to summarize per sub-client
* Example: recent files, total meetings, last LLM outputs
* Run LLM summaries on all sub-client records to provide executive overview to client (e.g., top action items across their customers)

---

## 🧪 Testing Flow (4 Steps)

1. Login or register user (Supabase)
2. Upload PDF / DOCX / JPG to a sub-client under their org
3. Generate summary or email using GPT-4 + sub-client context
4. Client dashboard aggregates sub-client data for overview

---

## ⏱️ Estimated Setup Time

| Task                     | Time        |
| ------------------------ | ----------- |
| Supabase Setup           | 30 mins     |
| FastAPI File Upload API  | 45 mins     |
| Embedding + Vector Store | 30 mins     |
| Streamlit UI             | 30 mins     |
| LLM Output Generation    | 30 mins     |
| **Total**                | **\~3 hrs** |

---

## ✅ MVP Checklist

* [ ] Multi-tenant setup
* [ ] File upload (PDF/DOCX/Image)
* [ ] Vector embedding per user
* [ ] Sub-client-level vector isolation
* [ ] LLM generates proposal / email / action
* [ ] Client dashboard to summarize sub-client data
* [ ] Basic UI for testing

---

## 🔗 Integration with Current Waitlist

The current waitlist backend can serve as the foundation for user registration and email notifications. The MVP will extend this by:

1. **User Management**: Upgrade from simple email collection to full user accounts
2. **Multi-tenancy**: Add client/sub-client hierarchy
3. **File Processing**: Add document upload and processing capabilities
4. **LLM Integration**: Add AI-powered content generation
5. **Dashboard**: Create client management interface

---

## 📋 Next Steps

1. **Phase 1**: Complete waitlist functionality (✅ Current)
2. **Phase 2**: Add Supabase integration for user management
3. **Phase 3**: Implement file upload and processing
4. **Phase 4**: Add LLM capabilities and vector storage
5. **Phase 5**: Build client dashboard and multi-tenant features

Let me know if you'd like a starter repo or deploy-ready version!
