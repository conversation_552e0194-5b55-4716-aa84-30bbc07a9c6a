# Environment Configuration for Lemur Multi-Tenant LLM Platform
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=8000
ENVIRONMENT=development

# Email Configuration (Required for sending thank you emails)
# For Gmail:
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password-here
FROM_EMAIL=<EMAIL>
FROM_NAME=Lemur

# For other email providers, update SMTP_SERVER and SMTP_PORT accordingly:
# Outlook/Hotmail: smtp-mail.outlook.com, port 587
# Yahoo: smtp.mail.yahoo.com, port 587
# Custom SMTP: your-smtp-server.com, port 587 or 465

# Supabase Configuration (Required for Phase 2+)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here

# OpenAI Configuration (Required for LLM features)
OPENAI_API_KEY=sk-your-openai-api-key-here

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# CORS Configuration (Optional)
ALLOWED_ORIGINS=http://localhost:3000,https://your-frontend-domain.com
