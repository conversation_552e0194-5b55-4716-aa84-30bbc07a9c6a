"""
File processing module for handling PDF, DOCX, and image files
"""

import os
import io
import tempfile
import logging
from typing import Optional, List, Dict, Any
from pathlib import Path

# File processing imports
import PyPDF2
from docx import Document
from PIL import Image
import pytesseract

# Vector processing imports
import openai
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
import chromadb

logger = logging.getLogger(__name__)

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200

# Initialize OpenAI
if OPENAI_API_KEY:
    openai.api_key = OPENAI_API_KEY
    try:
        embeddings = OpenAIEmbeddings(openai_api_key=OPENAI_API_KEY)
        logger.info("OpenAI embeddings initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize OpenAI embeddings: {e}")
        embeddings = None
else:
    logger.warning("OpenAI API key not found")
    embeddings = None

# Initialize ChromaDB with new configuration
try:
    chroma_client = chromadb.PersistentClient(path="./chroma_db")
    logger.info("ChromaDB client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize ChromaDB: {e}")
    chroma_client = None

class FileProcessor:
    """Handles file processing and text extraction"""
    
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=CHUNK_SIZE,
            chunk_overlap=CHUNK_OVERLAP,
            length_function=len,
        )
        self.embeddings = OpenAIEmbeddings() if OPENAI_API_KEY else None
    
    def extract_text_from_pdf(self, file_content: bytes) -> str:
        """Extract text from PDF file"""
        try:
            pdf_file = io.BytesIO(file_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            return ""
    
    def extract_text_from_docx(self, file_content: bytes) -> str:
        """Extract text from DOCX file"""
        try:
            docx_file = io.BytesIO(file_content)
            doc = Document(docx_file)
            
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            return ""
    
    def extract_text_from_image(self, file_content: bytes) -> str:
        """Extract text from image using OCR"""
        try:
            image = Image.open(io.BytesIO(file_content))
            text = pytesseract.image_to_string(image)
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from image: {e}")
            return ""
    
    def process_file(self, file_content: bytes, filename: str) -> str:
        """Process file and extract text based on file type"""
        file_extension = Path(filename).suffix.lower()

        if file_extension == '.pdf':
            return self.extract_text_from_pdf(file_content)
        elif file_extension == '.docx':
            return self.extract_text_from_docx(file_content)
        elif file_extension == '.txt':
            return file_content.decode('utf-8', errors='ignore')
        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            return self.extract_text_from_image(file_content)
        else:
            logger.warning(f"Unsupported file type: {file_extension}")
            return ""
    
    def split_text_into_chunks(self, text: str) -> List[str]:
        """Split text into chunks for vector storage"""
        if not text:
            return []
        
        chunks = self.text_splitter.split_text(text)
        return chunks
    
    def create_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Create embeddings for text chunks"""
        if not self.embeddings or not texts:
            return []
        
        try:
            embeddings = self.embeddings.embed_documents(texts)
            return embeddings
        except Exception as e:
            logger.error(f"Error creating embeddings: {e}")
            return []

class VectorStore:
    """Handles vector storage and retrieval using ChromaDB"""
    
    def __init__(self):
        self.client = chroma_client
        self.embeddings = embeddings  # Use the global embeddings variable
    
    def get_or_create_collection(self, client_id: str, sub_client_id: Optional[str] = None) -> Any:
        """Get or create a collection for a client/sub-client"""
        collection_name = f"client_{client_id}"
        if sub_client_id:
            collection_name += f"_sub_{sub_client_id}"

        try:
            # Create metadata without None values
            metadata = {"client_id": client_id}
            if sub_client_id:
                metadata["sub_client_id"] = sub_client_id

            collection = self.client.get_or_create_collection(
                name=collection_name,
                metadata=metadata
            )
            logger.info(f"Successfully created/retrieved collection: {collection_name}")
            return collection
        except Exception as e:
            logger.error(f"Error getting/creating collection: {e}")
            return None
    
    def store_embeddings(self,
                        texts: List[str],
                        client_id: str,
                        sub_client_id: Optional[str] = None,
                        file_id: Optional[str] = None) -> bool:
        """Store text chunks and their embeddings"""
        if not self.client:
            logger.error("ChromaDB client not available")
            return False

        if not self.embeddings:
            logger.error("OpenAI embeddings not available")
            return False

        if not texts:
            logger.error("No texts provided for embedding")
            return False
        
        try:
            collection = self.get_or_create_collection(client_id, sub_client_id)
            if not collection:
                return False
            
            # Create embeddings
            embeddings = self.embeddings.embed_documents(texts)
            
            # Create metadata for each chunk
            metadatas = []
            ids = []
            for i, chunk_text in enumerate(texts):
                metadata = {
                    "client_id": client_id,
                    "chunk_index": str(i)  # Convert to string for ChromaDB
                }

                # Only add non-empty values
                if sub_client_id:
                    metadata["sub_client_id"] = sub_client_id
                if file_id:
                    metadata["file_id"] = file_id

                metadatas.append(metadata)
                ids.append(f"{file_id}_{i}" if file_id else f"{client_id}_{i}")
            
            # Store in ChromaDB
            collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"Stored {len(texts)} chunks for client {client_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing embeddings: {e}")
            return False
    
    def search_similar(self, 
                      query: str, 
                      client_id: str, 
                      sub_client_id: Optional[str] = None,
                      n_results: int = 5) -> List[Dict[str, Any]]:
        """Search for similar text chunks"""
        if not self.embeddings:
            return []
        
        try:
            collection = self.get_or_create_collection(client_id, sub_client_id)
            if not collection:
                return []
            
            # Create query embedding
            query_embedding = self.embeddings.embed_query(query)
            
            # Search for similar chunks
            where_clause = {"client_id": {"$eq": client_id}}

            # Only add sub_client_id filter if it exists
            if sub_client_id:
                where_clause = {"$and": [
                    {"client_id": {"$eq": client_id}},
                    {"sub_client_id": {"$eq": sub_client_id}}
                ]}

            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=where_clause
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result = {
                        "text": doc,
                        "metadata": results['metadatas'][0][i] if results['metadatas'] else {},
                        "distance": results['distances'][0][i] if results['distances'] else 0
                    }
                    formatted_results.append(result)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching similar chunks: {e}")
            return []

# Global instances
file_processor = FileProcessor()
vector_store = VectorStore()

# Utility functions
async def process_and_store_file(file_content: bytes, 
                                filename: str,
                                client_id: str,
                                sub_client_id: Optional[str] = None,
                                file_id: Optional[str] = None) -> Dict[str, Any]:
    """Process a file and store its embeddings"""
    
    # Extract text
    extracted_text = file_processor.process_file(file_content, filename)
    if not extracted_text:
        return {
            "success": False,
            "error": "Could not extract text from file",
            "extracted_text": "",
            "chunks_stored": 0
        }
    
    # Split into chunks
    chunks = file_processor.split_text_into_chunks(extracted_text)
    if not chunks:
        return {
            "success": False,
            "error": "Could not split text into chunks",
            "extracted_text": extracted_text,
            "chunks_stored": 0
        }
    
    # Store embeddings
    success = vector_store.store_embeddings(chunks, client_id, sub_client_id, file_id)
    
    return {
        "success": success,
        "error": None if success else "Failed to store embeddings",
        "extracted_text": extracted_text,
        "chunks_stored": len(chunks) if success else 0
    }

def search_knowledge_base(query: str, 
                         client_id: str, 
                         sub_client_id: Optional[str] = None,
                         n_results: int = 5) -> List[Dict[str, Any]]:
    """Search the knowledge base for relevant information"""
    return vector_store.search_similar(query, client_id, sub_client_id, n_results)
