#!/usr/bin/env python3
"""
Comprehensive test script for Lemur Multi-Tenant LLM Platform
Tests: File uploads, embeddings, database operations, and insights
"""

import os
import sys
import json
import time
import requests
from pathlib import Path
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont

# Test configuration
API_BASE_URL = "http://localhost:8000"
TEST_USER_ID = "test-user-123"
TEST_CLIENT_ID = "test-client-123"
TEST_SUB_CLIENT_ID = "test-sub-client-123"

class PlatformTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, success, message="", data=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "data": data
        })
    
    def test_api_health(self):
        """Test API health and service availability"""
        print("\n🔍 Testing API Health...")
        
        try:
            response = self.session.get(f"{API_BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                services = health_data.get("services", {})
                
                self.log_test("API Health", True, f"API is running on port {health_data.get('port')}")
                
                # Check individual services
                for service, status in services.items():
                    self.log_test(f"Service: {service}", status, 
                                f"{'Available' if status else 'Not configured'}")
                
                return True
            else:
                self.log_test("API Health", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("API Health", False, f"Connection failed: {e}")
            return False
    
    def test_database_operations(self):
        """Test database operations"""
        print("\n🗄️ Testing Database Operations...")
        
        # Test user creation
        try:
            user_data = {
                "email": "<EMAIL>",
                "name": "Test User",
                "password": "testpassword123"
            }
            
            response = self.session.post(f"{API_BASE_URL}/users/register", json=user_data)
            if response.status_code in [200, 400]:  # 400 might mean user already exists
                result = response.json()
                if response.status_code == 200:
                    self.log_test("User Registration", True, "New user created")
                else:
                    self.log_test("User Registration", True, "User already exists (expected)")
            else:
                self.log_test("User Registration", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("User Registration", False, f"Error: {e}")
        
        # Test client creation
        try:
            client_data = {
                "name": "Test Client Organization",
                "description": "A test client for platform testing"
            }
            
            form_data = {
                "user_id": TEST_USER_ID,
                **client_data
            }
            
            response = self.session.post(f"{API_BASE_URL}/clients/create", data=form_data)
            if response.status_code == 200:
                self.log_test("Client Creation", True, "Client created successfully")
            else:
                self.log_test("Client Creation", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Client Creation", False, f"Error: {e}")
        
        # Test sub-client creation
        try:
            sub_client_data = {
                "name": "Test Sub-Client",
                "description": "A test sub-client",
                "client_id": TEST_CLIENT_ID,
                "contact_email": "<EMAIL>",
                "contact_name": "John Doe"
            }
            
            response = self.session.post(f"{API_BASE_URL}/sub-clients/create", json=sub_client_data)
            if response.status_code == 200:
                self.log_test("Sub-Client Creation", True, "Sub-client created successfully")
            else:
                self.log_test("Sub-Client Creation", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Sub-Client Creation", False, f"Error: {e}")
    
    def create_test_files(self):
        """Create test files for upload testing"""
        test_files_dir = Path("test_files")
        test_files_dir.mkdir(exist_ok=True)
        
        # Create a test text file (will be saved as .txt but we'll test as document)
        test_text = """
        Test Document for Lemur Platform
        
        This is a sample document to test the file processing capabilities.
        
        Key Points:
        - File upload functionality
        - Text extraction
        - Vector embedding generation
        - Multi-tenant data isolation
        
        Action Items:
        1. Test file upload endpoint
        2. Verify text extraction
        3. Check embedding storage
        4. Validate search functionality
        
        Contact Information:
        Email: <EMAIL>
        Phone: (*************
        """
        
        txt_file = test_files_dir / "test_document.txt"
        txt_file.write_text(test_text)
        
        # Create a test image with text
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        text = "Test Image for OCR\n\nThis image contains text that should be\nextracted using OCR technology.\n\nLemur Platform Test"
        draw.text((50, 50), text, fill='black', font=font)
        
        img_file = test_files_dir / "test_image.png"
        img.save(img_file)
        
        return {
            "text_file": txt_file,
            "image_file": img_file
        }
    
    def test_file_upload(self):
        """Test file upload and processing"""
        print("\n📁 Testing File Upload and Processing...")
        
        # Create test files
        test_files = self.create_test_files()
        
        for file_type, file_path in test_files.items():
            try:
                with open(file_path, 'rb') as f:
                    files = {'file': (file_path.name, f, 'application/octet-stream')}
                    data = {
                        'client_id': TEST_CLIENT_ID,
                        'sub_client_id': TEST_SUB_CLIENT_ID,
                        'user_id': TEST_USER_ID
                    }
                    
                    response = self.session.post(f"{API_BASE_URL}/files/upload", 
                                               files=files, data=data, timeout=30)
                    
                    if response.status_code == 200:
                        result = response.json()
                        self.log_test(f"File Upload ({file_type})", True, 
                                    f"Uploaded {file_path.name}, extracted {len(result.get('extracted_text', ''))} chars")
                        
                        # Store file ID for later tests
                        if file_type == "text_file":
                            self.test_file_id = result.get('file_id')
                    else:
                        self.log_test(f"File Upload ({file_type})", False, 
                                    f"HTTP {response.status_code}: {response.text}")
                        
            except Exception as e:
                self.log_test(f"File Upload ({file_type})", False, f"Error: {e}")
    
    def test_embeddings_and_search(self):
        """Test vector embeddings and search functionality"""
        print("\n🔍 Testing Embeddings and Search...")
        
        # Test LLM generation (which uses embeddings internally)
        test_prompts = [
            {
                "type": "summary",
                "prompt": "Summarize the uploaded documents",
                "endpoint": "/generate/summary"
            },
            {
                "type": "email",
                "prompt": "Write a professional email about the test results",
                "endpoint": "/generate/email"
            },
            {
                "type": "action_items",
                "prompt": "Extract action items from the documents",
                "endpoint": "/generate/action-items"
            }
        ]
        
        for test_case in test_prompts:
            try:
                request_data = {
                    "prompt": test_case["prompt"],
                    "client_id": TEST_CLIENT_ID,
                    "sub_client_id": TEST_SUB_CLIENT_ID,
                    "additional_params": {"user_id": TEST_USER_ID}
                }
                
                response = self.session.post(f"{API_BASE_URL}{test_case['endpoint']}", 
                                           json=request_data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    content_length = len(result.get('content', ''))
                    self.log_test(f"LLM Generation ({test_case['type']})", True, 
                                f"Generated {content_length} characters")
                else:
                    self.log_test(f"LLM Generation ({test_case['type']})", False, 
                                f"HTTP {response.status_code}: {response.text}")
                    
            except Exception as e:
                self.log_test(f"LLM Generation ({test_case['type']})", False, f"Error: {e}")
    
    def test_insights_and_analytics(self):
        """Test insights and analytics functionality"""
        print("\n📊 Testing Insights and Analytics...")
        
        # Test dashboard data retrieval
        try:
            response = self.session.get(f"{API_BASE_URL}/dashboard/{TEST_CLIENT_ID}")
            
            if response.status_code == 200:
                dashboard_data = response.json()
                if dashboard_data.get("status") == "success":
                    data = dashboard_data["dashboard"]
                    
                    self.log_test("Dashboard Data", True, 
                                f"Retrieved data for {len(data.get('sub_clients', []))} sub-clients")
                    
                    # Check specific metrics
                    total_files = data.get('total_files', 0)
                    total_outputs = data.get('total_outputs', 0)
                    
                    self.log_test("File Metrics", total_files > 0, 
                                f"Found {total_files} files")
                    self.log_test("Output Metrics", total_outputs > 0, 
                                f"Found {total_outputs} outputs")
                else:
                    self.log_test("Dashboard Data", False, "Invalid response format")
            else:
                self.log_test("Dashboard Data", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Dashboard Data", False, f"Error: {e}")
        
        # Test file listing
        try:
            response = self.session.get(f"{API_BASE_URL}/files/{TEST_CLIENT_ID}")
            
            if response.status_code == 200:
                files_data = response.json()
                files = files_data.get('files', [])
                self.log_test("File Listing", True, f"Retrieved {len(files)} files")
            else:
                self.log_test("File Listing", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("File Listing", False, f"Error: {e}")
        
        # Test output listing
        try:
            response = self.session.get(f"{API_BASE_URL}/outputs/{TEST_CLIENT_ID}")
            
            if response.status_code == 200:
                outputs_data = response.json()
                outputs = outputs_data.get('outputs', [])
                self.log_test("Output Listing", True, f"Retrieved {len(outputs)} outputs")
            else:
                self.log_test("Output Listing", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Output Listing", False, f"Error: {e}")
    
    def test_waitlist_functionality(self):
        """Test original waitlist functionality"""
        print("\n📧 Testing Waitlist Functionality...")
        
        try:
            waitlist_data = {
                "email": f"test+{int(time.time())}@example.com",
                "name": "Test User"
            }
            
            response = self.session.post(f"{API_BASE_URL}/submit", json=waitlist_data)
            
            if response.status_code == 200:
                result = response.json()
                email_sent = result.get('email_sent', False)
                self.log_test("Waitlist Registration", True, 
                            f"Registration successful, email sent: {email_sent}")
            else:
                self.log_test("Waitlist Registration", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Waitlist Registration", False, f"Error: {e}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Lemur Platform Comprehensive Test Suite")
        print("=" * 60)
        
        # Check if API is running
        if not self.test_api_health():
            print("\n❌ API is not running. Please start the server first:")
            print("   python main.py")
            return False
        
        # Run all test suites
        self.test_waitlist_functionality()
        self.test_database_operations()
        self.test_file_upload()
        self.test_embeddings_and_search()
        self.test_insights_and_analytics()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['success']]
        if failed_tests:
            print("\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"   • {test['test']}: {test['message']}")
        
        print("\n🎯 Platform Status:")
        if passed >= total * 0.8:  # 80% pass rate
            print("✅ Platform is working well!")
        elif passed >= total * 0.6:  # 60% pass rate
            print("⚠️  Platform has some issues but core functionality works")
        else:
            print("❌ Platform has significant issues")
        
        return passed >= total * 0.6

def main():
    """Main test function"""
    print("Starting comprehensive platform test...")
    
    # Check if server is likely running
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=2)
        if response.status_code != 200:
            print("⚠️  API server may not be running properly")
    except:
        print("❌ Cannot connect to API server. Please start it first:")
        print("   python launcher.py")
        print("   OR")
        print("   python main.py")
        return False
    
    tester = PlatformTester()
    success = tester.run_all_tests()
    
    print(f"\n{'✅ Tests completed successfully!' if success else '❌ Tests completed with issues.'}")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
