#!/bin/bash

# Basic functionality test for Lemur Platform
# Tests what's available without full database setup

API_BASE="http://localhost:8000"

echo "🧪 Lemur Platform - Basic Functionality Test"
echo "============================================="

# Function to print test headers
print_test() {
    echo ""
    echo "🔍 Testing: $1"
    echo "-----------------------------------"
}

# Test 1: API Health Check
print_test "API Health Check"
echo "Command: curl -X GET \"$API_BASE/health\""
curl -s -X GET "$API_BASE/health" | jq '.' 2>/dev/null || curl -s -X GET "$API_BASE/health"

# Test 2: Waitlist Registration (Core functionality)
print_test "Waitlist Registration"
echo "Command: curl -X POST \"$API_BASE/submit\" ..."
curl -s -X POST "$API_BASE/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User"
  }' | jq '.' 2>/dev/null || curl -s -X POST "$API_BASE/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User"
  }'

# Test 3: Get Submissions
print_test "Get Waitlist Submissions"
echo "Command: curl -X GET \"$API_BASE/submissions\""
curl -s -X GET "$API_BASE/submissions" | jq '.' 2>/dev/null || curl -s -X GET "$API_BASE/submissions"

# Test 4: User Registration (may work in demo mode)
print_test "User Registration (Demo Mode)"
echo "Command: curl -X POST \"$API_BASE/users/register\" ..."
curl -s -X POST "$API_BASE/users/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User",
    "password": "testpassword123"
  }' | jq '.' 2>/dev/null || curl -s -X POST "$API_BASE/users/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User",
    "password": "testpassword123"
  }'

# Test 5: Create a simple text file for testing
print_test "Creating Test Document"
cat > simple_test.txt << 'EOF'
Simple Test Document

This is a basic test document for the Lemur platform.

Key points:
- Test file upload
- Test text processing
- Verify basic functionality

Action items:
1. Upload this document
2. Process the content
3. Generate insights

Contact: <EMAIL>
EOF
echo "✅ Created simple_test.txt"

# Test 6: File Upload (if available)
print_test "File Upload Test"
echo "Command: curl -X POST \"$API_BASE/files/upload\" ..."
curl -s -X POST "$API_BASE/files/upload" \
  -F "file=@simple_test.txt" \
  -F "client_id=demo-client-123" \
  -F "sub_client_id=demo-sub-client-123" \
  -F "user_id=demo-user-123" | jq '.' 2>/dev/null || curl -s -X POST "$API_BASE/files/upload" \
  -F "file=@simple_test.txt" \
  -F "client_id=demo-client-123" \
  -F "sub_client_id=demo-sub-client-123" \
  -F "user_id=demo-user-123"

# Test 7: LLM Summary Generation (if available)
print_test "LLM Summary Generation"
echo "Command: curl -X POST \"$API_BASE/generate/summary\" ..."
curl -s -X POST "$API_BASE/generate/summary" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Summarize the uploaded test document",
    "client_id": "demo-client-123",
    "sub_client_id": "demo-sub-client-123",
    "additional_params": {
      "user_id": "demo-user-123"
    }
  }' | jq '.' 2>/dev/null || curl -s -X POST "$API_BASE/generate/summary" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Summarize the uploaded test document",
    "client_id": "demo-client-123",
    "sub_client_id": "demo-sub-client-123",
    "additional_params": {
      "user_id": "demo-user-123"
    }
  }'

# Test 8: Dashboard Analytics
print_test "Dashboard Analytics"
echo "Command: curl -X GET \"$API_BASE/dashboard/demo-client-123\""
curl -s -X GET "$API_BASE/dashboard/demo-client-123" | jq '.' 2>/dev/null || curl -s -X GET "$API_BASE/dashboard/demo-client-123"

# Cleanup
echo ""
echo "🧹 Cleaning up..."
rm -f simple_test.txt

echo ""
echo "✅ Basic functionality test completed!"
echo ""
echo "📊 What was tested:"
echo "  ✓ API health and availability"
echo "  ✓ Waitlist registration (core feature)"
echo "  ✓ User registration (demo mode)"
echo "  ✓ File upload (if configured)"
echo "  ✓ LLM generation (if configured)"
echo "  ✓ Dashboard analytics (if configured)"
echo ""
echo "🔧 Next steps:"
echo "  1. If Supabase features failed, configure your .env with Supabase credentials"
echo "  2. If LLM features failed, add your OpenAI API key to .env"
echo "  3. If file processing failed, install missing dependencies"
echo ""
echo "📖 See EMAIL_SETUP.md and .env.example for configuration help"
