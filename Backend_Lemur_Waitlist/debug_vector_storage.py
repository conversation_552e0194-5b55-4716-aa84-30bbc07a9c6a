#!/usr/bin/env python3
"""
Debug vector storage components
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_chromadb():
    """Test ChromaDB initialization"""
    print("🔍 Testing ChromaDB")
    print("=" * 30)
    
    try:
        import chromadb
        print("✅ ChromaDB imported successfully")
        
        # Test client creation
        client = chromadb.PersistentClient(path="./chroma_db")
        print("✅ ChromaDB client created successfully")
        
        # Test collection creation
        collection_name = "test_collection"
        try:
            collection = client.get_or_create_collection(collection_name)
            print(f"✅ Collection '{collection_name}' created/retrieved")
            
            # Test basic operations
            collection.add(
                documents=["This is a test document"],
                metadatas=[{"test": "metadata"}],
                ids=["test_id_1"]
            )
            print("✅ Document added to collection")
            
            # Test query
            results = collection.query(
                query_texts=["test document"],
                n_results=1
            )
            print(f"✅ Query successful, found {len(results['documents'][0])} results")
            
            # Cleanup
            client.delete_collection(collection_name)
            print("✅ Test collection cleaned up")
            
            return True
            
        except Exception as e:
            print(f"❌ Collection operations failed: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ ChromaDB import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ ChromaDB test failed: {e}")
        return False

def test_openai_embeddings():
    """Test OpenAI embeddings"""
    print("\n🔍 Testing OpenAI Embeddings")
    print("=" * 30)
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OpenAI API key not found")
        return False
    
    print(f"✅ OpenAI API key found: {api_key[:20]}...")
    
    try:
        from langchain_openai import OpenAIEmbeddings
        print("✅ OpenAIEmbeddings imported successfully")
        
        # Test embeddings creation
        embeddings = OpenAIEmbeddings(openai_api_key=api_key)
        print("✅ OpenAIEmbeddings instance created")
        
        # Test embedding generation
        test_texts = ["This is a test document", "Another test document"]
        embedding_vectors = embeddings.embed_documents(test_texts)
        print(f"✅ Generated embeddings for {len(test_texts)} documents")
        print(f"   Embedding dimensions: {len(embedding_vectors[0])}")
        
        # Test query embedding
        query_embedding = embeddings.embed_query("test query")
        print(f"✅ Generated query embedding with {len(query_embedding)} dimensions")
        
        return True
        
    except ImportError as e:
        print(f"❌ OpenAI embeddings import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ OpenAI embeddings test failed: {e}")
        return False

def test_file_processing():
    """Test file processing components"""
    print("\n🔍 Testing File Processing")
    print("=" * 30)
    
    try:
        from file_processor import file_processor, vector_store
        print("✅ File processor modules imported")
        
        # Test text processing
        test_content = """
        This is a test document for processing.
        It contains multiple paragraphs and should be split into chunks.
        
        This is the second paragraph with more content.
        We want to test the chunking functionality.
        """
        
        chunks = file_processor.split_text_into_chunks(test_content)
        print(f"✅ Text split into {len(chunks)} chunks")
        
        # Test vector store initialization
        if vector_store.client:
            print("✅ Vector store ChromaDB client available")
        else:
            print("❌ Vector store ChromaDB client not available")
            
        if vector_store.embeddings:
            print("✅ Vector store embeddings available")
        else:
            print("❌ Vector store embeddings not available")
            
        # Test embedding storage
        if vector_store.client and vector_store.embeddings:
            print("🧪 Testing embedding storage...")
            success = vector_store.store_embeddings(
                chunks, 
                client_id="test-client-123",
                sub_client_id="test-sub-123",
                file_id="test-file-123"
            )
            
            if success:
                print("✅ Embedding storage successful")
                
                # Test search
                search_results = vector_store.search_similar(
                    "test document",
                    client_id="test-client-123",
                    sub_client_id="test-sub-123",
                    n_results=2
                )
                print(f"✅ Search returned {len(search_results)} results")
                
                return True
            else:
                print("❌ Embedding storage failed")
                return False
        else:
            print("❌ Cannot test embedding storage - components not available")
            return False
            
    except ImportError as e:
        print(f"❌ File processor import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ File processing test failed: {e}")
        return False

def test_full_workflow():
    """Test the complete file processing workflow"""
    print("\n🔍 Testing Full Workflow")
    print("=" * 30)
    
    try:
        from file_processor import process_and_store_file
        print("✅ Process and store function imported")
        
        # Create test file content
        test_content = b"""QUARTERLY BUSINESS REPORT - Q4 2024

EXECUTIVE SUMMARY:
Our AI platform has shown remarkable growth this quarter.

KEY METRICS:
- Revenue: $5.1M (up 65%)
- Users: 85,000 (up 95%)
- Satisfaction: 4.9/5

ACHIEVEMENTS:
1. Launched AI document processing
2. Secured 30 enterprise clients
3. Reduced costs by 40%

ACTION ITEMS:
1. Scale infrastructure (Critical, DevOps, Jan 15)
2. Optimize mobile app (High, Mobile Team, Feb 1)
3. Redesign onboarding (High, UX Team, Jan 30)
"""
        
        # Test the full workflow
        import asyncio
        
        async def test_workflow():
            result = await process_and_store_file(
                test_content,
                "test_report.txt",
                "test-client-456",
                "test-sub-456",
                "test-file-456"
            )
            return result
        
        result = asyncio.run(test_workflow())
        
        if result["success"]:
            print("✅ Full workflow successful")
            print(f"   Extracted text length: {len(result['extracted_text'])}")
            print(f"   Chunks stored: {result['chunks_stored']}")
            return True
        else:
            print(f"❌ Full workflow failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Full workflow test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Vector Storage Debug Test")
    print("=" * 50)
    
    tests = [
        ("ChromaDB", test_chromadb),
        ("OpenAI Embeddings", test_openai_embeddings),
        ("File Processing", test_file_processing),
        ("Full Workflow", test_full_workflow),
    ]
    
    results = {}
    for name, test_func in tests:
        try:
            results[name] = test_func()
        except Exception as e:
            print(f"❌ {name} test crashed: {e}")
            results[name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DEBUG TEST SUMMARY")
    print("=" * 50)
    
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All vector storage components are working!")
    else:
        print("\n🔧 Issues found:")
        for name, result in results.items():
            if not result:
                print(f"  - {name} needs attention")

if __name__ == "__main__":
    main()
