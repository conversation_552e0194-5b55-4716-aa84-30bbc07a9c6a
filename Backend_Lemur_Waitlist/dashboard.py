"""
Streamlit Dashboard for Lemur Multi-Tenant LLM Platform
"""

import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json

# Configuration
API_BASE_URL = "http://localhost:8000"

# Page configuration
st.set_page_config(
    page_title="Lemur Dashboard",
    page_icon="🦎",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }
    .success-message {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
    }
    .error-message {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def make_api_request(endpoint, method="GET", data=None, files=None):
    """Make API request to the backend"""
    url = f"{API_BASE_URL}{endpoint}"
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            if files:
                response = requests.post(url, data=data, files=files)
            else:
                response = requests.post(url, json=data)
        
        if response.status_code == 200:
            return response.json(), None
        else:
            return None, f"Error {response.status_code}: {response.text}"
    except Exception as e:
        return None, f"Connection error: {str(e)}"

def main():
    """Main dashboard application"""
    
    # Header
    st.markdown('<h1 class="main-header">🦎 Lemur Multi-Tenant LLM Platform</h1>', unsafe_allow_html=True)
    
    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Dashboard", "File Upload", "LLM Generation", "Client Management", "Analytics"]
    )
    
    # Check API health
    health_data, health_error = make_api_request("/health")
    if health_error:
        st.error(f"⚠️ Cannot connect to API: {health_error}")
        st.stop()
    
    # Display service status
    with st.sidebar:
        st.subheader("Service Status")
        services = health_data.get("services", {})
        
        for service, status in services.items():
            if status:
                st.success(f"✅ {service.title()}")
            else:
                st.error(f"❌ {service.title()}")
    
    # Page routing
    if page == "Dashboard":
        show_dashboard()
    elif page == "File Upload":
        show_file_upload()
    elif page == "LLM Generation":
        show_llm_generation()
    elif page == "Client Management":
        show_client_management()
    elif page == "Analytics":
        show_analytics()

def show_dashboard():
    """Show main dashboard"""
    st.header("📊 Dashboard Overview")
    
    # Get client ID from user input
    client_id = st.text_input("Enter Client ID", value="demo-client-1")
    
    if client_id:
        # Get dashboard data
        dashboard_data, error = make_api_request(f"/dashboard/{client_id}")
        
        if error:
            st.error(f"Error loading dashboard: {error}")
            return
        
        if dashboard_data and dashboard_data.get("status") == "success":
            data = dashboard_data["dashboard"]
            
            # Metrics row
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Sub-Clients", len(data["sub_clients"]))
            
            with col2:
                st.metric("Total Files", data["total_files"])
            
            with col3:
                st.metric("Total Outputs", data["total_outputs"])
            
            with col4:
                avg_files = data["total_files"] / len(data["sub_clients"]) if data["sub_clients"] else 0
                st.metric("Avg Files/Client", f"{avg_files:.1f}")
            
            # Sub-clients overview
            st.subheader("Sub-Clients Overview")
            
            if data["sub_clients"]:
                for sub_client_data in data["sub_clients"]:
                    sub_client = sub_client_data["sub_client"]
                    
                    with st.expander(f"📁 {sub_client['name']}"):
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.write(f"**Description:** {sub_client.get('description', 'N/A')}")
                            st.write(f"**Contact:** {sub_client.get('contact_name', 'N/A')}")
                            st.write(f"**Email:** {sub_client.get('contact_email', 'N/A')}")
                        
                        with col2:
                            st.metric("Files", sub_client_data["file_count"])
                            st.metric("Outputs", sub_client_data["output_count"])
                        
                        # Recent activity
                        if sub_client_data["recent_files"]:
                            st.write("**Recent Files:**")
                            for file in sub_client_data["recent_files"][-3:]:
                                st.write(f"• {file['original_filename']}")
            else:
                st.info("No sub-clients found for this client.")

def show_file_upload():
    """Show file upload interface"""
    st.header("📁 File Upload")
    
    # Form for file upload
    with st.form("file_upload_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            client_id = st.text_input("Client ID", value="demo-client-1")
            user_id = st.text_input("User ID", value="demo-user-1")
        
        with col2:
            sub_client_id = st.text_input("Sub-Client ID (Optional)")
        
        uploaded_file = st.file_uploader(
            "Choose a file",
            type=['pdf', 'docx', 'jpg', 'jpeg', 'png', 'bmp', 'tiff']
        )
        
        submit_button = st.form_submit_button("Upload File")
        
        if submit_button and uploaded_file and client_id and user_id:
            with st.spinner("Uploading and processing file..."):
                # Prepare form data
                files = {"file": uploaded_file}
                data = {
                    "client_id": client_id,
                    "user_id": user_id
                }
                if sub_client_id:
                    data["sub_client_id"] = sub_client_id
                
                # Upload file
                response_data, error = make_api_request("/files/upload", method="POST", data=data, files=files)
                
                if error:
                    st.error(f"Upload failed: {error}")
                else:
                    st.success("✅ File uploaded and processed successfully!")
                    
                    if response_data:
                        st.write(f"**File ID:** {response_data.get('file_id')}")
                        st.write(f"**Chunks Stored:** {response_data.get('chunks_stored')}")
                        
                        if response_data.get('extracted_text'):
                            with st.expander("View Extracted Text"):
                                st.text(response_data['extracted_text'])

def show_llm_generation():
    """Show LLM content generation interface"""
    st.header("🤖 LLM Content Generation")
    
    # Form for LLM generation
    with st.form("llm_generation_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            client_id = st.text_input("Client ID", value="demo-client-1")
            sub_client_id = st.text_input("Sub-Client ID (Optional)")
        
        with col2:
            output_type = st.selectbox(
                "Output Type",
                ["email", "summary", "action-items"]
            )
        
        prompt = st.text_area(
            "Enter your prompt",
            placeholder="Describe what you want to generate...",
            height=100
        )
        
        # Additional parameters for email
        if output_type == "email":
            col1, col2 = st.columns(2)
            with col1:
                recipient_name = st.text_input("Recipient Name (Optional)")
            with col2:
                sender_name = st.text_input("Sender Name (Optional)")
        
        submit_button = st.form_submit_button("Generate Content")
        
        if submit_button and prompt and client_id:
            with st.spinner("Generating content..."):
                # Prepare request data
                request_data = {
                    "prompt": prompt,
                    "client_id": client_id,
                    "output_type": output_type
                }
                
                if sub_client_id:
                    request_data["sub_client_id"] = sub_client_id
                
                # Add additional parameters for email
                if output_type == "email":
                    additional_params = {}
                    if recipient_name:
                        additional_params["recipient_name"] = recipient_name
                    if sender_name:
                        additional_params["sender_name"] = sender_name
                    if additional_params:
                        request_data["additional_params"] = additional_params
                
                # Generate content
                endpoint = f"/generate/{output_type.replace('-', '-')}"
                response_data, error = make_api_request(endpoint, method="POST", data=request_data)
                
                if error:
                    st.error(f"Generation failed: {error}")
                else:
                    st.success("✅ Content generated successfully!")
                    
                    if response_data and response_data.get("content"):
                        st.subheader("Generated Content:")
                        st.markdown(response_data["content"])

def show_client_management():
    """Show client management interface"""
    st.header("👥 Client Management")
    
    tab1, tab2, tab3 = st.tabs(["Create Client", "Create Sub-Client", "View Clients"])
    
    with tab1:
        st.subheader("Create New Client")
        with st.form("create_client_form"):
            user_id = st.text_input("User ID", value="demo-user-1")
            client_name = st.text_input("Client Name")
            client_description = st.text_area("Description (Optional)")
            
            if st.form_submit_button("Create Client"):
                if client_name and user_id:
                    data = {
                        "name": client_name,
                        "description": client_description,
                        "user_id": user_id
                    }
                    
                    response_data, error = make_api_request("/clients/create", method="POST", data=data)
                    
                    if error:
                        st.error(f"Failed to create client: {error}")
                    else:
                        st.success("✅ Client created successfully!")
                        st.json(response_data)
    
    with tab2:
        st.subheader("Create New Sub-Client")
        with st.form("create_sub_client_form"):
            client_id = st.text_input("Parent Client ID")
            sub_client_name = st.text_input("Sub-Client Name")
            sub_client_description = st.text_area("Description (Optional)")
            contact_name = st.text_input("Contact Name (Optional)")
            contact_email = st.text_input("Contact Email (Optional)")
            
            if st.form_submit_button("Create Sub-Client"):
                if sub_client_name and client_id:
                    data = {
                        "name": sub_client_name,
                        "description": sub_client_description,
                        "client_id": client_id,
                        "contact_name": contact_name,
                        "contact_email": contact_email
                    }
                    
                    response_data, error = make_api_request("/sub-clients/create", method="POST", data=data)
                    
                    if error:
                        st.error(f"Failed to create sub-client: {error}")
                    else:
                        st.success("✅ Sub-client created successfully!")
                        st.json(response_data)
    
    with tab3:
        st.subheader("View Clients")
        user_id = st.text_input("Enter User ID to view clients", value="demo-user-1")
        
        if st.button("Load Clients"):
            response_data, error = make_api_request(f"/clients/{user_id}")
            
            if error:
                st.error(f"Failed to load clients: {error}")
            else:
                clients = response_data.get("clients", [])
                if clients:
                    for client in clients:
                        with st.expander(f"Client: {client['name']}"):
                            st.json(client)
                else:
                    st.info("No clients found for this user.")

def show_analytics():
    """Show analytics and insights"""
    st.header("📈 Analytics")
    
    client_id = st.text_input("Client ID for Analytics", value="demo-client-1")
    
    if client_id:
        # Get dashboard data for analytics
        dashboard_data, error = make_api_request(f"/dashboard/{client_id}")
        
        if error:
            st.error(f"Error loading analytics: {error}")
            return
        
        if dashboard_data and dashboard_data.get("status") == "success":
            data = dashboard_data["dashboard"]
            
            # Create charts
            if data["sub_clients"]:
                # Files per sub-client chart
                sub_client_names = [sc["sub_client"]["name"] for sc in data["sub_clients"]]
                file_counts = [sc["file_count"] for sc in data["sub_clients"]]
                output_counts = [sc["output_count"] for sc in data["sub_clients"]]
                
                col1, col2 = st.columns(2)
                
                with col1:
                    fig1 = px.bar(
                        x=sub_client_names,
                        y=file_counts,
                        title="Files per Sub-Client",
                        labels={"x": "Sub-Client", "y": "Number of Files"}
                    )
                    st.plotly_chart(fig1, use_container_width=True)
                
                with col2:
                    fig2 = px.bar(
                        x=sub_client_names,
                        y=output_counts,
                        title="Outputs per Sub-Client",
                        labels={"x": "Sub-Client", "y": "Number of Outputs"}
                    )
                    st.plotly_chart(fig2, use_container_width=True)
                
                # Summary metrics
                st.subheader("Summary Metrics")
                total_activity = sum(file_counts) + sum(output_counts)
                
                metrics_data = {
                    "Metric": ["Total Files", "Total Outputs", "Total Activity", "Active Sub-Clients"],
                    "Value": [sum(file_counts), sum(output_counts), total_activity, len(data["sub_clients"])]
                }
                
                df = pd.DataFrame(metrics_data)
                st.dataframe(df, use_container_width=True)
            else:
                st.info("No data available for analytics.")

if __name__ == "__main__":
    main()
