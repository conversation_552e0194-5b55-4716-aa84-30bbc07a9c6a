from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import json
import logging
from datetime import datetime
from pathlib import Path
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import asyncio
from concurrent.futures import ThreadPoolExecutor
import aiofiles
import uuid

# Local imports
from email_templates import get_welcome_email_template
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Optional imports for advanced features
try:
    from database import db, init_supabase, User, Client, SubClient, File as FileModel, Output
    SUPABASE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Supabase features not available: {e}")
    SUPABASE_AVAILABLE = False
    db = None
    init_supabase = lambda: False

try:
    from file_processor import process_and_store_file
    FILE_PROCESSING_AVAILABLE = True
except ImportError as e:
    logger.warning(f"File processing features not available: {e}")
    FILE_PROCESSING_AVAILABLE = False
    process_and_store_file = None

try:
    from llm_service import generate_email, generate_summary, generate_action_items, generate_custom_content
    LLM_AVAILABLE = True
except ImportError as e:
    logger.warning(f"LLM features not available: {e}")
    LLM_AVAILABLE = False
    generate_email = generate_summary = generate_action_items = generate_custom_content = None

# Environment variables
PORT = int(os.getenv("PORT", 8000))
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")

# Email configuration
SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.gmail.com")
SMTP_PORT = int(os.getenv("SMTP_PORT", 587))
SMTP_USERNAME = os.getenv("SMTP_USERNAME", "")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "")
FROM_EMAIL = os.getenv("FROM_EMAIL", SMTP_USERNAME)
FROM_NAME = os.getenv("FROM_NAME", "Lemur Waitlist")

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY", "")

# OpenAI configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")

# File upload configuration
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", 10 * 1024 * 1024))  # 10MB default
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "./uploads")

# Initialize services
init_supabase()

app = FastAPI(
    title="Waitlist API",
    description="API for handling waitlist form submissions",
    version="1.0.0"
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["GET", "POST", "DELETE"],  # Added DELETE method
    allow_headers=["Content-Type", "Authorization", "Accept", "Origin", "X-Requested-With"],
    expose_headers=["Content-Length"],
    max_age=600,
)

# Thread pool for async email sending
email_executor = ThreadPoolExecutor(max_workers=3)

# Pydantic models for API requests

class WaitlistSubmission(BaseModel):
    email: str
    name: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "name": "John Doe"
            }
        }

class UserCreate(BaseModel):
    email: str
    name: Optional[str] = None
    password: str

class ClientCreate(BaseModel):
    name: str
    description: Optional[str] = None

class SubClientCreate(BaseModel):
    name: str
    description: Optional[str] = None
    contact_email: Optional[str] = None
    contact_name: Optional[str] = None
    client_id: str

class LLMRequest(BaseModel):
    prompt: str
    client_id: str
    sub_client_id: Optional[str] = None
    output_type: str = "summary"  # email, summary, action_items, custom
    additional_params: Optional[Dict[str, Any]] = None

class FileUploadResponse(BaseModel):
    success: bool
    file_id: Optional[str] = None
    message: str
    extracted_text: Optional[str] = None
    chunks_stored: int = 0

def create_thank_you_email(user_email: str, user_name: Optional[str] = None) -> MIMEMultipart:
    """Create a personalized thank you email for waitlist registration."""

    # Get email template
    template = get_welcome_email_template(user_name)

    # Create message
    msg = MIMEMultipart('alternative')
    msg['Subject'] = template['subject']
    msg['From'] = f"{FROM_NAME} <{FROM_EMAIL}>"
    msg['To'] = user_email

    # Add both text and HTML parts
    text_part = MIMEText(template['text'], 'plain')
    html_part = MIMEText(template['html'], 'html')

    msg.attach(text_part)
    msg.attach(html_part)

    return msg

def send_email_sync(user_email: str, user_name: Optional[str] = None) -> bool:
    """Send thank you email synchronously."""
    try:
        # Check if email configuration is available
        if not SMTP_USERNAME or not SMTP_PASSWORD:
            logger.warning("Email credentials not configured, skipping email send")
            return False

        # Create email
        msg = create_thank_you_email(user_email, user_name)

        # Send email
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            server.send_message(msg)

        logger.info(f"Thank you email sent successfully to {user_email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send email to {user_email}: {str(e)}")
        return False

async def send_thank_you_email(user_email: str, user_name: Optional[str] = None) -> bool:
    """Send thank you email asynchronously."""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(email_executor, send_email_sync, user_email, user_name)

# Create submissions.json if it doesn't exist
SUBMISSIONS_FILE = Path("submissions.json")
if not SUBMISSIONS_FILE.exists():
    SUBMISSIONS_FILE.write_text(json.dumps([], indent=2))

def read_submissions() -> List[dict]:
    """Read submissions from JSON file with error handling"""
    try:
        return json.loads(SUBMISSIONS_FILE.read_text())
    except json.JSONDecodeError:
        logger.error("Invalid JSON in submissions file, resetting to empty list")
        SUBMISSIONS_FILE.write_text(json.dumps([], indent=2))
        return []
    except Exception as e:
        logger.error(f"Error reading submissions file: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to read submissions")

def write_submissions(submissions: List[dict]) -> None:
    """Write submissions to JSON file with error handling"""
    try:
        SUBMISSIONS_FILE.write_text(json.dumps(submissions, indent=2))
    except Exception as e:
        logger.error(f"Error writing to submissions file: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to save submission")

@app.get("/submissions", response_model=List[dict])
async def get_submissions() -> List[dict]:
    """Get all waitlist submissions."""
    return read_submissions()

@app.get("/download-submissions")
async def download_submissions():
    """Download all submissions as a JSON file."""
    try:
        submissions = read_submissions()
        return JSONResponse(
            content=submissions,
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename=submissions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            }
        )
    except Exception as e:
        logger.error(f"Failed to prepare download: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to prepare download")

@app.post("/submit", response_model=dict)
async def submit(submission: WaitlistSubmission) -> dict:
    """Handle waitlist form submissions and store in JSON file."""
    try:
        logger.info(f"Received submission from: {submission.email} (name: {submission.name})")

        submissions = read_submissions()

        # Check for duplicate email
        existing_emails = [sub.get("email") for sub in submissions]
        if submission.email in existing_emails:
            logger.warning(f"Duplicate email submission attempted: {submission.email}")
            return {
                "status": "info",
                "message": "You're already on our waitlist! We'll be in touch soon."
            }

        # Calculate the next ID (1-based indexing)
        next_id = len(submissions) + 1

        submission_data = {
            "id": next_id,
            "email": submission.email,
            "name": submission.name,
            "timestamp": datetime.now().isoformat()
        }

        # Append to the end of the list
        submissions.append(submission_data)
        write_submissions(submissions)

        # Send thank you email asynchronously
        email_sent = False
        try:
            email_sent = await send_thank_you_email(submission.email, submission.name)
        except Exception as email_error:
            logger.error(f"Email sending failed for {submission.email}: {str(email_error)}")
            # Don't fail the registration if email fails

        logger.info(f"Successfully added submission for {submission.email} (email sent: {email_sent})")

        # Customize response message based on name
        if submission.name:
            message = f"Thank you, {submission.name}, for joining our waitlist! Check your email for a welcome message."
        else:
            message = "Thank you for joining our waitlist! Check your email for a welcome message."

        if not email_sent:
            message += " (Note: Welcome email could not be sent, but you're successfully registered!)"

        return {
            "status": "success",
            "message": message,
            "email_sent": email_sent
        }

    except Exception as e:
        logger.error(f"Failed to process submission: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.delete("/submissions/{submission_id}")
async def delete_submission(submission_id: int) -> dict:
    """Delete a submission by its ID."""
    try:
        submissions = read_submissions()
        
        # Find the submission with the given ID
        submission_index = next(
            (index for index, sub in enumerate(submissions) if sub["id"] == submission_id),
            None
        )
        
        if submission_index is None:
            raise HTTPException(status_code=404, detail=f"Submission with ID {submission_id} not found")
        
        # Remove the submission
        deleted_submission = submissions.pop(submission_index)
        
        # Update IDs for remaining submissions
        for i, submission in enumerate(submissions, start=1):
            submission["id"] = i
        
        # Save the updated submissions
        write_submissions(submissions)
        
        logger.info(f"Successfully deleted submission with ID {submission_id}")
        return {
            "status": "success",
            "message": f"Submission {submission_id} deleted successfully",
            "deleted_submission": deleted_submission
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete submission: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete submission")

@app.get("/health")
async def health_check() -> dict:
    """Health check endpoint"""
    return {
        "status": "healthy",
        "environment": ENVIRONMENT,
        "port": PORT,
        "timestamp": datetime.now().isoformat(),
        "services": {
            "supabase": bool(SUPABASE_URL and SUPABASE_ANON_KEY and SUPABASE_AVAILABLE),
            "openai": bool(OPENAI_API_KEY and LLM_AVAILABLE),
            "email": bool(SMTP_USERNAME and SMTP_PASSWORD),
            "file_processing": FILE_PROCESSING_AVAILABLE
        },
        "features": {
            "waitlist": True,
            "user_management": SUPABASE_AVAILABLE,
            "file_upload": FILE_PROCESSING_AVAILABLE,
            "llm_generation": LLM_AVAILABLE,
            "dashboard": True
        }
    }

# ============================================================================
# Phase 2: User Management Endpoints
# ============================================================================

@app.post("/users/register")
async def register_user(user: UserCreate) -> dict:
    """Register a new user"""
    try:
        logger.info(f"Attempting to register user: {user.email}")

        if not SUPABASE_AVAILABLE or not db:
            logger.warning("Supabase not available, using demo mode")
            return {
                "status": "success",
                "message": "User registered successfully (demo mode)",
                "user_id": f"demo-user-{user.email.split('@')[0]}",
                "note": "Demo mode - configure Supabase for full functionality"
            }

        # Check if user already exists
        existing_user = await db.get_user_by_email(user.email)
        if existing_user:
            logger.warning(f"User already exists: {user.email}")
            raise HTTPException(status_code=400, detail="User already exists")

        # Create new user
        user_data = User(
            email=user.email,
            name=user.name,
            created_at=datetime.now()
        )

        created_user = await db.create_user(user_data)
        if not created_user:
            raise HTTPException(status_code=500, detail="Failed to create user")

        logger.info(f"User created successfully: {created_user.get('id')}")
        return {
            "status": "success",
            "message": "User registered successfully",
            "user_id": created_user.get("id")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error registering user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")

@app.post("/clients/create")
async def create_client(
    name: str = Form(...),
    description: str = Form(...),
    user_id: str = Form(...)
) -> dict:
    """Create a new client organization"""
    try:
        client_data = Client(
            name=name,
            description=description,
            user_id=user_id,
            created_at=datetime.now()
        )

        created_client = await db.create_client(client_data)
        if not created_client:
            raise HTTPException(status_code=500, detail="Failed to create client")

        return {
            "status": "success",
            "message": "Client created successfully",
            "client": created_client
        }

    except Exception as e:
        logger.error(f"Error creating client: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/clients/{user_id}")
async def get_user_clients(user_id: str) -> dict:
    """Get all clients for a user"""
    try:
        clients = await db.get_clients_by_user(user_id)
        return {
            "status": "success",
            "clients": clients
        }

    except Exception as e:
        logger.error(f"Error getting clients: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/sub-clients/create")
async def create_sub_client(sub_client: SubClientCreate) -> dict:
    """Create a new sub-client"""
    try:
        sub_client_data = SubClient(
            name=sub_client.name,
            description=sub_client.description,
            client_id=sub_client.client_id,
            contact_email=sub_client.contact_email,
            contact_name=sub_client.contact_name,
            created_at=datetime.now()
        )

        created_sub_client = await db.create_sub_client(sub_client_data)
        if not created_sub_client:
            raise HTTPException(status_code=500, detail="Failed to create sub-client")

        return {
            "status": "success",
            "message": "Sub-client created successfully",
            "sub_client": created_sub_client
        }

    except Exception as e:
        logger.error(f"Error creating sub-client: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/sub-clients/{client_id}")
async def get_client_sub_clients(client_id: str) -> dict:
    """Get all sub-clients for a client"""
    try:
        sub_clients = await db.get_sub_clients_by_client(client_id)
        return {
            "status": "success",
            "sub_clients": sub_clients
        }

    except Exception as e:
        logger.error(f"Error getting sub-clients: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# ============================================================================
# Phase 3: File Upload and Processing Endpoints
# ============================================================================

@app.post("/files/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    client_id: str = Form(...),
    sub_client_id: Optional[str] = Form(None),
    user_id: str = Form(...)
) -> FileUploadResponse:
    """Upload and process a file"""
    try:
        # Validate file size
        if file.size and file.size > MAX_FILE_SIZE:
            raise HTTPException(status_code=413, detail="File too large")

        # Validate file type
        allowed_extensions = {'.pdf', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_extension}. Allowed: {', '.join(allowed_extensions)}")

        # Read file content
        file_content = await file.read()

        # Generate unique file ID
        file_id = str(uuid.uuid4())

        # Create upload directory if it doesn't exist
        os.makedirs(UPLOAD_DIR, exist_ok=True)

        # Save file to disk
        file_path = os.path.join(UPLOAD_DIR, f"{file_id}_{file.filename}")
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)

        # Process file and extract text
        logger.info(f"Starting file processing for: {file.filename}")
        logger.info(f"File size: {len(file_content)} bytes")
        logger.info(f"Client ID: {client_id}, Sub-client ID: {sub_client_id}, File ID: {file_id}")

        try:
            processing_result = await process_and_store_file(
                file_content, file.filename, client_id, sub_client_id, file_id
            )
            logger.info(f"Processing result: {processing_result}")
        except Exception as e:
            logger.error(f"Exception during file processing: {str(e)}")
            # Fallback to basic processing
            file_extension = Path(file.filename).suffix.lower()
            if file_extension == '.txt':
                extracted_text = file_content.decode('utf-8', errors='ignore')
                processing_result = {
                    "success": True,
                    "extracted_text": extracted_text,
                    "chunks_stored": 1,
                    "error": None
                }
                logger.info("Used fallback text extraction")
            else:
                processing_result = {
                    "success": False,
                    "error": f"Processing failed: {str(e)}",
                    "extracted_text": "",
                    "chunks_stored": 0
                }

        if not processing_result["success"]:
            logger.error(f"File processing failed: {processing_result.get('error', 'Unknown error')}")
            return FileUploadResponse(
                success=False,
                message=f"Failed to store embeddings: {processing_result.get('error', 'Unknown error')}",
                chunks_stored=0
            )

        # Create file record in database
        file_record = FileModel(
            id=file_id,
            filename=f"{file_id}_{file.filename}",
            original_filename=file.filename,
            file_type=file_extension,
            file_size=len(file_content),
            storage_path=file_path,
            client_id=client_id,
            sub_client_id=sub_client_id,
            user_id=user_id,
            processed=True,
            extracted_text=processing_result["extracted_text"],
            created_at=datetime.now()
        )

        created_file = await db.create_file_record(file_record)

        return FileUploadResponse(
            success=True,
            file_id=file_id,
            message="File uploaded and processed successfully",
            extracted_text=processing_result["extracted_text"][:500] + "..." if len(processing_result["extracted_text"]) > 500 else processing_result["extracted_text"],
            chunks_stored=processing_result["chunks_stored"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/files/{client_id}")
async def get_client_files(client_id: str, sub_client_id: Optional[str] = None) -> dict:
    """Get files for a client/sub-client"""
    try:
        files = await db.get_files_by_client(client_id, sub_client_id)
        return {
            "status": "success",
            "files": files
        }

    except Exception as e:
        logger.error(f"Error getting files: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# ============================================================================
# Phase 4: LLM Content Generation Endpoints
# ============================================================================

@app.post("/generate/email")
async def generate_email_content(request: LLMRequest) -> dict:
    """Generate email content using LLM"""
    try:
        additional_params = request.additional_params or {}
        result = await generate_email(
            request.prompt,
            request.client_id,
            request.sub_client_id,
            recipient_name=additional_params.get("recipient_name"),
            sender_name=additional_params.get("sender_name")
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        # Save output to database
        output_record = Output(
            title=f"Email: {request.prompt[:50]}...",
            content=result["content"],
            output_type="email",
            prompt=request.prompt,
            client_id=request.client_id,
            sub_client_id=request.sub_client_id,
            user_id=additional_params.get("user_id", ""),
            created_at=datetime.now()
        )

        await db.create_output(output_record)

        return {
            "status": "success",
            "content": result["content"],
            "type": "email"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating email: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/generate/summary")
async def generate_summary_content(request: LLMRequest) -> dict:
    """Generate summary content using LLM"""
    try:
        result = await generate_summary(
            request.prompt,
            request.client_id,
            request.sub_client_id
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        # Save output to database
        output_record = Output(
            title=f"Summary: {request.prompt[:50]}...",
            content=result["content"],
            output_type="summary",
            prompt=request.prompt,
            client_id=request.client_id,
            sub_client_id=request.sub_client_id,
            user_id=request.additional_params.get("user_id", "") if request.additional_params else "",
            created_at=datetime.now()
        )

        await db.create_output(output_record)

        return {
            "status": "success",
            "content": result["content"],
            "type": "summary"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/generate/action-items")
async def generate_action_items_content(request: LLMRequest) -> dict:
    """Generate action items using LLM"""
    try:
        result = await generate_action_items(
            request.prompt,
            request.client_id,
            request.sub_client_id
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        # Save output to database
        output_record = Output(
            title=f"Action Items: {request.prompt[:50]}...",
            content=result["content"],
            output_type="action_items",
            prompt=request.prompt,
            client_id=request.client_id,
            sub_client_id=request.sub_client_id,
            user_id=request.additional_params.get("user_id", "") if request.additional_params else "",
            created_at=datetime.now()
        )

        await db.create_output(output_record)

        return {
            "status": "success",
            "content": result["content"],
            "type": "action_items"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating action items: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/outputs/{client_id}")
async def get_client_outputs(client_id: str, sub_client_id: Optional[str] = None) -> dict:
    """Get LLM outputs for a client/sub-client"""
    try:
        outputs = await db.get_outputs_by_client(client_id, sub_client_id)
        return {
            "status": "success",
            "outputs": outputs
        }

    except Exception as e:
        logger.error(f"Error getting outputs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# ============================================================================
# Dashboard Analytics Endpoint
# ============================================================================

@app.get("/dashboard/{client_id}")
async def get_client_dashboard(client_id: str) -> dict:
    """Get dashboard data for a client"""
    try:
        # Get all sub-clients
        sub_clients = await db.get_sub_clients_by_client(client_id)

        # Get files and outputs for each sub-client
        dashboard_data = {
            "client_id": client_id,
            "sub_clients": [],
            "total_files": 0,
            "total_outputs": 0,
            "recent_activity": []
        }

        for sub_client in sub_clients:
            sub_client_id = sub_client["id"]

            # Get files and outputs for this sub-client
            files = await db.get_files_by_client(client_id, sub_client_id)
            outputs = await db.get_outputs_by_client(client_id, sub_client_id)

            sub_client_data = {
                "sub_client": sub_client,
                "file_count": len(files),
                "output_count": len(outputs),
                "recent_files": files[-5:] if files else [],
                "recent_outputs": outputs[-5:] if outputs else []
            }

            dashboard_data["sub_clients"].append(sub_client_data)
            dashboard_data["total_files"] += len(files)
            dashboard_data["total_outputs"] += len(outputs)

        return {
            "status": "success",
            "dashboard": dashboard_data
        }

    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting server on port {PORT} with allowed origins: *")
    uvicorn.run(app, host="0.0.0.0", port=PORT)