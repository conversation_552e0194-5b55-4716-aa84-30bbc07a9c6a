# 🚀 Lemur Platform - Quick Start Guide

Get the complete multi-tenant LLM platform running in under 10 minutes!

## ⚡ Super Quick Start

```bash
# 1. Setup everything
./setup_dev.sh

# 2. Configure credentials (see below)
nano .env

# 3. Start everything
./run_all.sh          # Linux/macOS
python launcher.py     # Cross-platform
run_all.bat          # Windows
```

That's it! 🎉

## 🔑 Essential Credentials

Edit your `.env` file with these **minimum required** credentials:

### For Email (Phase 1)
```env
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-gmail-app-password
FROM_EMAIL=<EMAIL>
```

### For Database (Phase 2+)
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
```

### For AI Features (Phase 3+)
```env
OPENAI_API_KEY=sk-your-openai-api-key
```

## 🌐 Access Your Platform

Once running, access these URLs:

- **🏠 Dashboard**: http://localhost:8501
- **🔧 API Docs**: http://localhost:8000/docs
- **⚡ API Health**: http://localhost:8000/health

## 🧪 Test Everything

```bash
# Test the API
python test_api.py

# Test waitlist registration
curl -X POST "http://localhost:8000/submit" \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "name": "Test User"}'
```

## 📋 What You Can Do Now

### 📧 Phase 1: Waitlist
- ✅ Collect email signups
- ✅ Send personalized thank you emails
- ✅ Prevent duplicate registrations

### 👥 Phase 2: User Management
- ✅ Create user accounts
- ✅ Manage client organizations
- ✅ Set up sub-client hierarchies

### 📁 Phase 3: File Processing
- ✅ Upload PDF, DOCX, and image files
- ✅ Extract text with OCR
- ✅ Store as vector embeddings

### 🤖 Phase 4: AI Content Generation
- ✅ Generate emails from documents
- ✅ Create summaries
- ✅ Extract action items
- ✅ Custom content generation

### 📊 Phase 5: Analytics Dashboard
- ✅ View client activity
- ✅ Monitor file uploads
- ✅ Track AI generations
- ✅ Visualize usage patterns

## 🔧 Getting Credentials

### Gmail App Password
1. Enable 2FA on your Google account
2. Go to Google Account → Security → App passwords
3. Generate password for "Mail"
4. Use this as `SMTP_PASSWORD`

### Supabase Setup
1. Create account at [supabase.com](https://supabase.com)
2. Create new project
3. Go to Settings → API
4. Copy URL and anon key
5. Run SQL from `supabase_setup.sql`

### OpenAI API Key
1. Create account at [platform.openai.com](https://platform.openai.com)
2. Add billing information
3. Go to API Keys
4. Create new key

## 🆘 Troubleshooting

### Services Won't Start
```bash
# Check if ports are free
lsof -i :8000  # FastAPI
lsof -i :8501  # Streamlit

# Kill existing processes
pkill -f "python main.py"
pkill -f "streamlit run"
```

### Dependencies Missing
```bash
# Reinstall everything
rm -rf venv
./setup_dev.sh
```

### Database Issues
```bash
# Check Supabase connection
curl -H "apikey: YOUR_ANON_KEY" \
     "https://your-project.supabase.co/rest/v1/users"
```

### Email Not Working
- Make sure you're using an **App Password**, not your regular Gmail password
- Check that 2FA is enabled on your Google account
- Verify SMTP settings in `.env`

## 🎯 Next Steps

1. **Upload a test file** in the dashboard
2. **Generate AI content** from your documents
3. **Create client organizations** for multi-tenancy
4. **Explore the analytics** dashboard
5. **Customize email templates** in `email_templates.py`

## 📚 Full Documentation

- **Complete Setup**: `README.md`
- **Email Configuration**: `EMAIL_SETUP.md`
- **Development Roadmap**: `MVP_ROADMAP.md`
- **Database Schema**: `supabase_setup.sql`

## 🎉 You're Ready!

Your complete multi-tenant LLM platform is now running with:
- ✅ User management and authentication
- ✅ File processing and AI generation
- ✅ Beautiful web dashboard
- ✅ Full API with documentation
- ✅ Multi-tenant data isolation
- ✅ Email notifications

Welcome to the future of AI-powered business tools! 🚀
