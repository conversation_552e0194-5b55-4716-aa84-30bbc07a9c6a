#!/usr/bin/env python3
"""
Simplified version of main.py for testing core functionality
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import json
import logging
from datetime import datetime
from pathlib import Path
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import asyncio
from concurrent.futures import ThreadPoolExecutor
import aiofiles
import uuid

# Local imports
from email_templates import get_welcome_email_template
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
PORT = int(os.getenv("PORT", 8000))
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")

# Email configuration
SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.gmail.com")
SMTP_PORT = int(os.getenv("SMTP_PORT", 587))
SMTP_USERNAME = os.getenv("SMTP_USERNAME", "")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "")
FROM_EMAIL = os.getenv("FROM_EMAIL", SMTP_USERNAME)
FROM_NAME = os.getenv("FROM_NAME", "Lemur")

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY", "")

# OpenAI configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")

# File upload configuration
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", 10 * 1024 * 1024))  # 10MB default
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "./uploads")

# Create FastAPI app
app = FastAPI(
    title="Lemur Multi-Tenant LLM Platform",
    description="A comprehensive platform for document processing and AI-powered insights",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class WaitlistSubmission(BaseModel):
    email: str
    name: Optional[str] = None

class FileUploadResponse(BaseModel):
    success: bool
    file_id: Optional[str] = None
    message: str
    extracted_text: Optional[str] = None
    chunks_stored: int = 0

class LLMRequest(BaseModel):
    prompt: str
    client_id: str
    sub_client_id: Optional[str] = None
    output_type: str = "summary"
    additional_params: Optional[Dict[str, Any]] = None

# Global variables
submissions_file = "submissions.json"
executor = ThreadPoolExecutor(max_workers=2)

# Utility functions
def load_submissions() -> List[dict]:
    """Load submissions from JSON file"""
    try:
        if Path(submissions_file).exists():
            with open(submissions_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error loading submissions: {e}")
    return []

def save_submissions(submissions: List[dict]) -> bool:
    """Save submissions to JSON file"""
    try:
        with open(submissions_file, 'w') as f:
            json.dump(submissions, f, indent=2, default=str)
        return True
    except Exception as e:
        logger.error(f"Error saving submissions: {e}")
        return False

def send_email(to_email: str, subject: str, html_content: str) -> bool:
    """Send email using SMTP"""
    if not all([SMTP_USERNAME, SMTP_PASSWORD, FROM_EMAIL]):
        logger.warning("Email credentials not configured")
        return False
    
    try:
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = f"{FROM_NAME} <{FROM_EMAIL}>"
        msg['To'] = to_email
        
        html_part = MIMEText(html_content, 'html')
        msg.attach(html_part)
        
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            server.send_message(msg)
        
        return True
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        return False

async def send_email_async(to_email: str, subject: str, html_content: str) -> bool:
    """Send email asynchronously"""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(executor, send_email, to_email, subject, html_content)

# API Endpoints

@app.get("/health")
async def health_check() -> dict:
    """Health check endpoint"""
    return {
        "status": "healthy",
        "environment": ENVIRONMENT,
        "port": PORT,
        "timestamp": datetime.now().isoformat(),
        "services": {
            "supabase": bool(SUPABASE_URL and SUPABASE_ANON_KEY),
            "openai": bool(OPENAI_API_KEY),
            "email": bool(SMTP_USERNAME and SMTP_PASSWORD),
            "file_processing": True  # Simplified version
        },
        "features": {
            "waitlist": True,
            "user_management": bool(SUPABASE_URL and SUPABASE_ANON_KEY),
            "file_upload": True,  # Simplified version
            "llm_generation": bool(OPENAI_API_KEY),
            "dashboard": True
        }
    }

@app.post("/submit")
async def submit_waitlist(submission: WaitlistSubmission) -> dict:
    """Submit waitlist form"""
    try:
        submissions = load_submissions()
        
        # Check for duplicate email
        existing_emails = [s.get('email', '').lower() for s in submissions]
        if submission.email.lower() in existing_emails:
            return {
                "status": "info",
                "message": "You're already on our waitlist! We'll be in touch soon.",
                "email_sent": False
            }
        
        # Create new submission
        new_submission = {
            "email": submission.email,
            "name": submission.name,
            "timestamp": datetime.now().isoformat(),
            "id": str(uuid.uuid4())
        }
        
        submissions.append(new_submission)
        
        # Save to file
        if not save_submissions(submissions):
            raise HTTPException(status_code=500, detail="Failed to save submission")
        
        # Send welcome email
        email_sent = False
        if SMTP_USERNAME and SMTP_PASSWORD:
            try:
                html_content = get_welcome_email_template(submission.name or "there")
                email_sent = await send_email_async(
                    submission.email,
                    "Welcome to Lemur - You're on the waitlist!",
                    html_content
                )
            except Exception as e:
                logger.error(f"Error sending email: {e}")
        
        return {
            "status": "success",
            "message": "Thank you for joining our waitlist!",
            "email_sent": email_sent
        }
        
    except Exception as e:
        logger.error(f"Error processing submission: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/submissions")
async def get_submissions() -> List[dict]:
    """Get all submissions"""
    return load_submissions()

@app.post("/files/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    client_id: str = Form(...),
    sub_client_id: Optional[str] = Form(None),
    user_id: str = Form(...)
) -> FileUploadResponse:
    """Upload and process a file (simplified version)"""
    try:
        # Validate file size
        if file.size and file.size > MAX_FILE_SIZE:
            raise HTTPException(status_code=413, detail="File too large")
        
        # Validate file type
        allowed_extensions = {'.pdf', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_extension}")
        
        # Read file content
        file_content = await file.read()
        
        # Generate unique file ID
        file_id = str(uuid.uuid4())
        
        # Create upload directory if it doesn't exist
        os.makedirs(UPLOAD_DIR, exist_ok=True)
        
        # Save file to disk
        file_path = os.path.join(UPLOAD_DIR, f"{file_id}_{file.filename}")
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        # Simple text extraction
        extracted_text = ""
        if file_extension == '.txt':
            extracted_text = file_content.decode('utf-8', errors='ignore')
        else:
            extracted_text = f"File uploaded: {file.filename} ({len(file_content)} bytes)"
        
        logger.info(f"File uploaded successfully: {file_id}")
        
        return FileUploadResponse(
            success=True,
            file_id=file_id,
            message="File uploaded successfully",
            extracted_text=extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
            chunks_stored=1  # Simplified
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/generate/summary")
async def generate_summary_content(request: LLMRequest) -> dict:
    """Generate summary content (simplified version)"""
    try:
        if not OPENAI_API_KEY:
            return {
                "status": "info",
                "content": f"Demo Summary: This is a simulated summary for the prompt: '{request.prompt}'. Configure OpenAI API key for real AI generation.",
                "type": "summary"
            }
        
        # Simple OpenAI integration
        import openai
        openai.api_key = OPENAI_API_KEY
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that creates comprehensive summaries."},
                {"role": "user", "content": request.prompt}
            ],
            max_tokens=500
        )
        
        content = response.choices[0].message.content
        
        return {
            "status": "success",
            "content": content,
            "type": "summary"
        }
        
    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        return {
            "status": "error",
            "content": f"Demo Summary: Error occurred - {str(e)}. This is a fallback response.",
            "type": "summary"
        }

@app.post("/generate/email")
async def generate_email_content(request: LLMRequest) -> dict:
    """Generate email content (simplified version)"""
    try:
        additional_params = request.additional_params or {}
        recipient = additional_params.get("recipient_name", "Valued Client")
        sender = additional_params.get("sender_name", "Team")
        
        if not OPENAI_API_KEY:
            demo_email = f"""Subject: {request.prompt}

Dear {recipient},

This is a demo email generated based on your request: "{request.prompt}"

Configure your OpenAI API key to enable real AI-powered email generation.

Best regards,
{sender}"""
            
            return {
                "status": "success",
                "content": demo_email,
                "type": "email"
            }
        
        # Simple OpenAI integration for email
        import openai
        openai.api_key = OPENAI_API_KEY
        
        email_prompt = f"Write a professional email about: {request.prompt}. Recipient: {recipient}, Sender: {sender}"
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a professional email writer."},
                {"role": "user", "content": email_prompt}
            ],
            max_tokens=400
        )
        
        content = response.choices[0].message.content
        
        return {
            "status": "success",
            "content": content,
            "type": "email"
        }
        
    except Exception as e:
        logger.error(f"Error generating email: {e}")
        return {
            "status": "error",
            "content": f"Demo email generation failed: {str(e)}",
            "type": "email"
        }

@app.post("/generate/action-items")
async def generate_action_items_content(request: LLMRequest) -> dict:
    """Generate action items (simplified version)"""
    try:
        if not OPENAI_API_KEY:
            demo_items = f"""Action Items for: {request.prompt}

1. Review and analyze the request (Priority: High, Due: Next week)
2. Configure OpenAI API key for real AI generation (Priority: Critical, Due: ASAP)
3. Test the full functionality (Priority: Medium, Due: This week)
4. Deploy to production (Priority: Low, Due: Next month)

Note: This is a demo response. Configure OpenAI for real AI-powered action item extraction."""
            
            return {
                "status": "success",
                "content": demo_items,
                "type": "action_items"
            }
        
        # Simple OpenAI integration for action items
        import openai
        openai.api_key = OPENAI_API_KEY
        
        action_prompt = f"Extract action items from: {request.prompt}. Format as numbered list with priorities and deadlines."
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are an expert at extracting actionable tasks from text."},
                {"role": "user", "content": action_prompt}
            ],
            max_tokens=400
        )
        
        content = response.choices[0].message.content
        
        return {
            "status": "success",
            "content": content,
            "type": "action_items"
        }
        
    except Exception as e:
        logger.error(f"Error generating action items: {e}")
        return {
            "status": "error",
            "content": f"Demo action items generation failed: {str(e)}",
            "type": "action_items"
        }

@app.get("/dashboard/{client_id}")
async def get_client_dashboard(client_id: str) -> dict:
    """Get dashboard data for a client (simplified version)"""
    try:
        # Simulate dashboard data
        dashboard_data = {
            "client_id": client_id,
            "sub_clients": [
                {
                    "sub_client": {
                        "id": "demo-sub-1",
                        "name": "Demo Sub-Client A",
                        "description": "First demo sub-client"
                    },
                    "file_count": 3,
                    "output_count": 5,
                    "recent_files": [],
                    "recent_outputs": []
                }
            ],
            "total_files": 3,
            "total_outputs": 5,
            "recent_activity": []
        }
        
        return {
            "status": "success",
            "dashboard": dashboard_data
        }
        
    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting simplified server on port {PORT}")
    uvicorn.run(app, host="0.0.0.0", port=PORT)
