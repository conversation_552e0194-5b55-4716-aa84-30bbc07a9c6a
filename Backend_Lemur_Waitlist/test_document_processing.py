#!/usr/bin/env python3
"""
Direct test of document processing and insights functionality
Bypasses the API to test core features
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_file_processing():
    """Test file processing functionality"""
    print("🔍 Testing File Processing...")
    
    try:
        # Test if we can import file processing modules
        from file_processor import file_processor, vector_store
        print("✅ File processing modules imported successfully")
        
        # Create a test document
        test_content = """
        BUSINESS QUARTERLY REPORT - Q4 2024
        
        EXECUTIVE SUMMARY:
        Our AI platform has shown remarkable growth this quarter with significant improvements in user engagement and revenue generation.
        
        KEY METRICS:
        - Monthly Active Users: 75,000 (↑85% from Q3)
        - Revenue: $4.2M (↑55% from Q3)
        - Customer Satisfaction: 4.9/5
        - Platform Uptime: 99.95%
        
        MAJOR ACHIEVEMENTS:
        1. Successfully launched AI document processing feature
        2. Onboarded 25 enterprise clients
        3. Reduced infrastructure costs by 35%
        4. Achieved SOC 2 Type II compliance
        
        CHALLENGES:
        1. Scaling issues during peak traffic
        2. Mobile app performance optimization needed
        3. Customer onboarding complexity
        4. API rate limiting concerns
        
        ACTION ITEMS:
        1. Implement auto-scaling infrastructure (Priority: Critical, Owner: DevOps, Due: Jan 15)
        2. Optimize mobile performance (Priority: High, Owner: Mobile Team, Due: Feb 1)
        3. Simplify onboarding process (Priority: High, Owner: UX Team, Due: Jan 30)
        4. Upgrade API infrastructure (Priority: Medium, Owner: Backend Team, Due: Feb 15)
        
        FINANCIAL PROJECTIONS:
        - Q1 2025 Target: $6M revenue
        - Expected growth: 100,000 MAU
        - Infrastructure investment: $1.2M
        
        STRATEGIC RECOMMENDATIONS:
        - Expand AI capabilities with GPT-4 integration
        - Develop enterprise-specific features
        - Consider international expansion
        - Implement advanced analytics dashboard
        
        Contact: CEO Sarah Johnson (<EMAIL>)
        """
        
        # Test text processing
        print("📄 Processing test document...")
        chunks = file_processor.split_text_into_chunks(test_content)
        print(f"✅ Document split into {len(chunks)} chunks")
        
        # Test vector storage
        print("🔍 Testing vector storage...")
        success = vector_store.store_embeddings(
            chunks, 
            client_id="test-client-123", 
            sub_client_id="test-sub-123",
            file_id="test-file-123"
        )
        
        if success:
            print("✅ Vector embeddings stored successfully")
        else:
            print("❌ Vector storage failed")
            
        # Test search functionality
        print("🔍 Testing vector search...")
        search_results = vector_store.search_similar(
            "What are the key achievements and metrics?",
            client_id="test-client-123",
            sub_client_id="test-sub-123",
            n_results=3
        )
        
        print(f"✅ Search returned {len(search_results)} results")
        if search_results:
            print("   Sample result:", search_results[0]['text'][:100] + "...")
            
        return True
        
    except ImportError as e:
        print(f"❌ File processing not available: {e}")
        return False
    except Exception as e:
        print(f"❌ File processing test failed: {e}")
        return False

def test_llm_generation():
    """Test LLM generation functionality"""
    print("\n🤖 Testing LLM Generation...")
    
    try:
        from llm_service import llm_service
        print("✅ LLM service imported successfully")
        
        # Test summary generation
        print("📝 Testing summary generation...")
        summary_result = await llm_service.generate_summary(
            "Summarize the quarterly business report and highlight key achievements",
            client_id="test-client-123",
            sub_client_id="test-sub-123"
        )
        
        if summary_result["success"]:
            print("✅ Summary generated successfully")
            print(f"   Length: {len(summary_result['content'])} characters")
        else:
            print(f"❌ Summary generation failed: {summary_result['error']}")
            
        # Test email generation
        print("📧 Testing email generation...")
        email_result = await llm_service.generate_email(
            "Write a professional email to investors about Q4 results",
            client_id="test-client-123",
            sub_client_id="test-sub-123",
            recipient_name="Investors",
            sender_name="CEO"
        )
        
        if email_result["success"]:
            print("✅ Email generated successfully")
            print(f"   Length: {len(email_result['content'])} characters")
        else:
            print(f"❌ Email generation failed: {email_result['error']}")
            
        # Test action items generation
        print("📋 Testing action items generation...")
        action_result = await llm_service.generate_action_items(
            "Extract all action items with priorities and deadlines",
            client_id="test-client-123",
            sub_client_id="test-sub-123"
        )
        
        if action_result["success"]:
            print("✅ Action items generated successfully")
            print(f"   Length: {len(action_result['content'])} characters")
        else:
            print(f"❌ Action items generation failed: {action_result['error']}")
            
        return True
        
    except ImportError as e:
        print(f"❌ LLM service not available: {e}")
        return False
    except Exception as e:
        print(f"❌ LLM generation test failed: {e}")
        return False

def test_supabase_direct():
    """Test direct Supabase operations"""
    print("\n🗄️ Testing Direct Supabase Operations...")
    
    try:
        from supabase import create_client
        
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_ANON_KEY")
        
        if not supabase_url or not supabase_key:
            print("❌ Supabase credentials not configured")
            return False
            
        supabase = create_client(supabase_url, supabase_key)
        
        # Test user creation
        print("👤 Testing user creation...")
        user_data = {
            "email": "<EMAIL>",
            "name": "Direct Test User"
        }
        
        # Delete if exists
        supabase.table("users").delete().eq("email", "<EMAIL>").execute()
        
        # Create user
        user_result = supabase.table("users").insert(user_data).execute()
        if user_result.data:
            user_id = user_result.data[0]['id']
            print(f"✅ User created: {user_id}")
            
            # Test client creation
            print("🏢 Testing client creation...")
            client_data = {
                "name": "Direct Test Client",
                "description": "Testing direct operations",
                "user_id": user_id
            }
            
            client_result = supabase.table("clients").insert(client_data).execute()
            if client_result.data:
                client_id = client_result.data[0]['id']
                print(f"✅ Client created: {client_id}")
                
                # Test file record creation
                print("📁 Testing file record creation...")
                file_data = {
                    "filename": "test_document.txt",
                    "original_filename": "test_document.txt",
                    "file_type": ".txt",
                    "file_size": 1000,
                    "storage_path": "/test/path",
                    "client_id": client_id,
                    "user_id": user_id,
                    "extracted_text": "Test document content"
                }
                
                file_result = supabase.table("files").insert(file_data).execute()
                if file_result.data:
                    file_id = file_result.data[0]['id']
                    print(f"✅ File record created: {file_id}")
                    
                    # Test output creation
                    print("📊 Testing output creation...")
                    output_data = {
                        "title": "Test Summary",
                        "content": "This is a test summary generated by AI",
                        "output_type": "summary",
                        "client_id": client_id,
                        "user_id": user_id,
                        "file_id": file_id
                    }
                    
                    output_result = supabase.table("outputs").insert(output_data).execute()
                    if output_result.data:
                        output_id = output_result.data[0]['id']
                        print(f"✅ Output created: {output_id}")
                        
                        # Clean up
                        print("🧹 Cleaning up test data...")
                        supabase.table("outputs").delete().eq("id", output_id).execute()
                        supabase.table("files").delete().eq("id", file_id).execute()
                        supabase.table("clients").delete().eq("id", client_id).execute()
                        supabase.table("users").delete().eq("id", user_id).execute()
                        print("✅ Cleanup completed")
                        
                        return True
        
        print("❌ Database operations failed")
        return False
        
    except Exception as e:
        print(f"❌ Supabase test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Lemur Platform - Core Functionality Test")
    print("=" * 60)
    
    # Test file processing
    file_processing_ok = test_file_processing()
    
    # Test LLM generation
    llm_ok = await test_llm_generation() if file_processing_ok else False
    
    # Test Supabase operations
    supabase_ok = test_supabase_direct()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    print(f"File Processing: {'✅ PASS' if file_processing_ok else '❌ FAIL'}")
    print(f"LLM Generation: {'✅ PASS' if llm_ok else '❌ FAIL'}")
    print(f"Database Operations: {'✅ PASS' if supabase_ok else '❌ FAIL'}")
    
    if file_processing_ok and llm_ok and supabase_ok:
        print("\n🎉 All core features are working!")
        print("\n📋 What works:")
        print("  ✅ Document text processing and chunking")
        print("  ✅ Vector embeddings and search")
        print("  ✅ AI content generation (summaries, emails, action items)")
        print("  ✅ Database operations (users, clients, files, outputs)")
        print("\n🔧 The API endpoints need debugging, but core functionality is solid!")
    else:
        print("\n⚠️ Some features need configuration:")
        if not file_processing_ok:
            print("  - Install file processing dependencies")
        if not llm_ok:
            print("  - Configure OpenAI API key")
        if not supabase_ok:
            print("  - Check Supabase configuration")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
