# Lemur Waitlist Backend

A FastAPI backend service for the Lemur waitlist that handles form submissions, sends personalized thank you emails, and serves as the foundation for a multi-tenant LLM-powered B2B platform.

## 🚀 Project Vision

This waitlist backend is **Phase 1** of building a comprehensive multi-tenant LLM platform. See [MVP_ROADMAP.md](MVP_ROADMAP.md) for the complete development plan.

## Features

### 🎯 Phase 1: Waitlist (Complete)
- ✅ Form submission endpoint (`POST /submit`)
- ✅ Data validation using Pydantic
- ✅ JSON file storage
- ✅ CORS support
- ✅ Health check endpoint
- ✅ Error handling and logging
- ✅ **Automated thank you emails** with personalized messages
- ✅ **Duplicate email detection**
- ✅ **Asynchronous email sending** (non-blocking)
- ✅ **Beautiful HTML email templates**

### 👥 Phase 2: User Management (Complete)
- ✅ **Supabase integration** for database and auth
- ✅ **User registration and management**
- ✅ **Client organization management**
- ✅ **Sub-client hierarchy**
- ✅ **Row-level security (RLS)** for data isolation

### 📁 Phase 3: File Processing & LLM (Complete)
- ✅ **Multi-format file upload** (PDF, DOCX, Images)
- ✅ **OCR text extraction** from images
- ✅ **Document parsing** and text extraction
- ✅ **Vector embeddings** with ChromaDB
- ✅ **OpenAI GPT-4 integration**
- ✅ **Context-aware content generation**
- ✅ **Email, summary, and action item generation**

### 📊 Phase 4: Dashboard & Analytics (Complete)
- ✅ **Streamlit dashboard** for client management
- ✅ **File upload interface**
- ✅ **LLM content generation UI**
- ✅ **Analytics and reporting**
- ✅ **Multi-tenant data visualization**

## Quick Start

### Automated Setup (Recommended)

```bash
# 1. Run the setup script
./setup_dev.sh

# 2. Edit your credentials
nano .env

# 3. Start everything (API + Dashboard)
./run_all.sh          # Linux/macOS
python launcher.py     # Cross-platform
run_all.bat          # Windows
```

### Manual Setup

1. Create virtual environment:

```bash
python3 -m venv venv
source venv/bin/activate
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your email credentials (see EMAIL_SETUP.md for detailed instructions)
```

4. Start all services:

```bash
# Option 1: All-in-one launcher (RECOMMENDED)
python launcher.py

# Option 2: Individual services
python main.py                    # API server
streamlit run dashboard.py        # Dashboard (separate terminal)
```

### Testing

Test all API endpoints:

```bash
# Make sure server is running first
python test_api.py
```

📧 **For email functionality**: See [EMAIL_SETUP.md](EMAIL_SETUP.md) for detailed email configuration instructions.

## API Endpoints

### POST /submit

Submit waitlist form data.

Request body:

```json
{
  "email": "string",
  "name": "string (optional)"
}
```

Example:
```json
{
  "email": "<EMAIL>",
  "name": "John Doe"
}
```

### GET /submissions

Get all submissions.

### GET /health

Health check endpoint.

## Deployment on Render

1. Create a new Web Service on Render
2. Connect your GitHub repository
3. Use the following settings:

   - **Name**: waitlist-api (or your preferred name)
   - **Environment**: Python
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `uvicorn main:app --host 0.0.0.0 --port $PORT`
   - **Python Version**: 3.11.0

4. Add the following environment variables in Render dashboard:

   - `PYTHON_VERSION`: 3.11.0
   - `SMTP_USERNAME`: Your email address (e.g., <EMAIL>)
   - `SMTP_PASSWORD`: Your email app password (for Gmail, generate an App Password)
   - `FROM_EMAIL`: Email address to send from (usually same as SMTP_USERNAME)
   - `FROM_NAME`: Display name for emails (e.g., "Lemur Waitlist")

5. Deploy!

## Email Configuration

The application sends personalized thank you emails to users who join the waitlist. To enable email functionality:

### For Gmail:
1. Enable 2-factor authentication on your Google account
2. Generate an App Password: Google Account → Security → 2-Step Verification → App passwords
3. Use your Gmail address as `SMTP_USERNAME` and the generated app password as `SMTP_PASSWORD`

### For other email providers:
- Update `SMTP_SERVER` and `SMTP_PORT` in the environment variables
- Use your email provider's SMTP settings

### Environment Variables:
- `SMTP_SERVER`: SMTP server address (default: smtp.gmail.com)
- `SMTP_PORT`: SMTP port (default: 587)
- `SMTP_USERNAME`: Your email address
- `SMTP_PASSWORD`: Your email password or app password
- `FROM_EMAIL`: Email address to send from
- `FROM_NAME`: Display name for outgoing emails

**Note**: If email credentials are not configured, the application will still work but won't send emails.

## Data Storage

Submissions are stored in a `submissions.json` file in the project root. Each submission includes:

- Email address
- Name (optional)
- Timestamp of submission
- Unique ID

## Security Notes

- CORS is currently set to allow all origins (`*`). In production, this should be restricted to specific domains.
- Consider adding rate limiting for the `/submit` endpoint in production.
- The `submissions.json` file should be backed up regularly in production.

## 🗺️ Development Roadmap

This waitlist backend is the foundation for a larger multi-tenant LLM platform:

### ✅ Phase 1: Waitlist (Complete)
- [x] Email collection with validation
- [x] Personalized thank you emails
- [x] Duplicate detection
- [x] JSON storage with timestamps
- [x] Admin dashboard for submissions

### ✅ Phase 2: User Management (Complete)
- [x] Supabase integration
- [x] User authentication
- [x] Client/organization management
- [x] Database migration from JSON
- [x] Row-level security (RLS)

### ✅ Phase 3: LLM Platform (Complete)
- [x] File upload (PDF, DOCX, Images)
- [x] Document processing and OCR
- [x] Vector embeddings and storage
- [x] Multi-tenant data isolation
- [x] LLM-powered content generation

### ✅ Phase 4: Dashboard & Analytics (Complete)
- [x] Client management dashboard
- [x] Usage analytics
- [x] Streamlit web interface
- [x] Advanced reporting

### 🚀 Future Enhancements
- [ ] Real-time collaboration
- [ ] Advanced AI models
- [ ] Billing integration
- [ ] Mobile app
- [ ] API rate limiting
- [ ] Advanced security features

**📖 Full details**: See [MVP_ROADMAP.md](MVP_ROADMAP.md) for complete implementation guide.

## Project Structure

```
.
├── main.py              # Main FastAPI application with all endpoints
├── database.py          # Supabase database models and operations
├── file_processor.py    # File processing and vector storage
├── llm_service.py       # OpenAI LLM integration
├── email_templates.py   # Email template functions
├── dashboard.py         # Streamlit dashboard application
├── launcher.py         # Cross-platform launcher for all services
├── run_all.sh          # Linux/macOS launcher script
├── run_all.bat         # Windows launcher script
├── test_api.py         # API testing script
├── setup_dev.sh        # Development environment setup script
├── requirements.txt     # Python dependencies
├── render.yaml         # Render configuration
├── Procfile           # Process file for Render
├── supabase_setup.sql  # Database schema and setup
├── submissions.json    # Waitlist submissions (auto-generated)
├── .env.example        # Environment variables template
├── .env               # Your environment variables (create from .env.example)
├── uploads/           # File upload directory (auto-generated)
├── chroma_db/         # Vector database storage (auto-generated)
├── venv/              # Virtual environment (auto-generated)
├── EMAIL_SETUP.md      # Email configuration guide
├── MVP_ROADMAP.md      # Complete platform development roadmap
└── README.md          # This file
```
