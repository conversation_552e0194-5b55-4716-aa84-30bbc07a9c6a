@echo off
REM Lemur Multi-Tenant LLM Platform - Windows Startup Script

echo 🚀 Starting Lemur Multi-Tenant LLM Platform...

REM Check if virtual environment exists
if not exist "venv" (
    echo ❌ Virtual environment not found. Please run setup_dev.sh first
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo ❌ .env file not found. Please create it from .env.example
    pause
    exit /b 1
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

REM Create necessary directories
if not exist "uploads" mkdir uploads
if not exist "chroma_db" mkdir chroma_db

REM Start services using Python launcher
echo 🌐 Starting all services...
python launcher.py

pause
