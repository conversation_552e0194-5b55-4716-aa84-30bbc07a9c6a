#!/bin/bash

# Lemur Multi-Tenant LLM Platform - Complete Startup Script
# This script starts both the FastAPI backend and Streamlit dashboard

echo "🚀 Starting Lemur Multi-Tenant LLM Platform..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run ./setup_dev.sh first"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create it from .env.example"
    exit 1
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Check if required packages are installed
echo "📦 Checking dependencies..."
python -c "import fastapi, streamlit, supabase, openai" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Some dependencies are missing. Installing..."
    pip install -r requirements.txt
fi

# Create necessary directories
mkdir -p uploads
mkdir -p chroma_db

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    if [ ! -z "$FASTAPI_PID" ]; then
        kill $FASTAPI_PID 2>/dev/null
        echo "   ✅ FastAPI server stopped"
    fi
    if [ ! -z "$STREAMLIT_PID" ]; then
        kill $STREAMLIT_PID 2>/dev/null
        echo "   ✅ Streamlit dashboard stopped"
    fi
    echo "👋 Goodbye!"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start FastAPI server in background
echo "🌐 Starting FastAPI server on http://localhost:8000..."
python main.py &
FASTAPI_PID=$!

# Wait a moment for FastAPI to start
sleep 3

# Check if FastAPI started successfully
curl -s http://localhost:8000/health > /dev/null
if [ $? -eq 0 ]; then
    echo "   ✅ FastAPI server started successfully"
else
    echo "   ❌ FastAPI server failed to start"
    kill $FASTAPI_PID 2>/dev/null
    exit 1
fi

# Start Streamlit dashboard in background
echo "📊 Starting Streamlit dashboard on http://localhost:8501..."
streamlit run dashboard.py --server.headless true --server.port 8501 &
STREAMLIT_PID=$!

# Wait a moment for Streamlit to start
sleep 5

# Check if Streamlit started successfully
curl -s http://localhost:8501 > /dev/null
if [ $? -eq 0 ]; then
    echo "   ✅ Streamlit dashboard started successfully"
else
    echo "   ⚠️  Streamlit dashboard may still be starting..."
fi

echo ""
echo "🎉 All services are running!"
echo ""
echo "📍 Access Points:"
echo "   🌐 FastAPI Backend:     http://localhost:8000"
echo "   📚 API Documentation:   http://localhost:8000/docs"
echo "   📊 Streamlit Dashboard: http://localhost:8501"
echo ""
echo "🧪 Quick Tests:"
echo "   curl http://localhost:8000/health"
echo "   python test_api.py"
echo ""
echo "📖 Features Available:"
echo "   ✅ Waitlist registration with email notifications"
echo "   ✅ User and client management"
echo "   ✅ File upload and processing (PDF, DOCX, Images)"
echo "   ✅ LLM content generation (emails, summaries, action items)"
echo "   ✅ Interactive dashboard and analytics"
echo ""
echo "Press Ctrl+C to stop all services"
echo ""

# Keep the script running and monitor processes
while true; do
    # Check if FastAPI is still running
    if ! kill -0 $FASTAPI_PID 2>/dev/null; then
        echo "❌ FastAPI server stopped unexpectedly"
        cleanup
    fi
    
    # Check if Streamlit is still running
    if ! kill -0 $STREAMLIT_PID 2>/dev/null; then
        echo "❌ Streamlit dashboard stopped unexpectedly"
        cleanup
    fi
    
    sleep 5
done
