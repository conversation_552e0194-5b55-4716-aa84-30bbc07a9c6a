#!/bin/bash

# Lemur Platform - Comprehensive cURL Test Script
# Tests document processing and insights functionality

API_BASE="http://localhost:8000"
TEST_USER_ID="test-user-123"
TEST_CLIENT_ID="test-client-123"
TEST_SUB_CLIENT_ID="test-sub-client-123"

echo "🧪 Lemur Platform cURL Test Suite"
echo "=================================="

# Function to print test headers
print_test() {
    echo ""
    echo "🔍 Testing: $1"
    echo "-----------------------------------"
}

# Function to create a test file
create_test_file() {
    cat > test_document.txt << 'EOF'
LEMUR PLATFORM TEST DOCUMENT

This is a comprehensive test document for the Lemur multi-tenant LLM platform.

EXECUTIVE SUMMARY:
The Lemur platform successfully processes documents and generates AI-powered insights.

KEY FEATURES TESTED:
- File upload and processing
- Text extraction capabilities
- Vector embedding generation
- Multi-tenant data isolation
- LLM-powered content generation

ACTION ITEMS:
1. Verify file upload endpoint functionality
2. Test text extraction from various file formats
3. Validate vector embedding storage
4. Check multi-tenant data isolation
5. Generate AI-powered summaries and emails

CONTACT INFORMATION:
Project Manager: John Doe
Email: <EMAIL>
Phone: (*************

NEXT STEPS:
- Deploy to production environment
- Implement advanced analytics
- Add real-time collaboration features
- Integrate billing system

This document contains sufficient content to test the platform's ability to extract meaningful information and generate relevant insights.
EOF
    echo "📄 Created test_document.txt"
}

# Test 1: API Health Check
print_test "API Health Check"
curl -s -X GET "$API_BASE/health" | jq '.' || echo "❌ Health check failed"

# Test 2: Waitlist Registration (Basic functionality)
print_test "Waitlist Registration"
curl -s -X POST "$API_BASE/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User"
  }' | jq '.' || echo "❌ Waitlist registration failed"

# Test 3: User Registration
print_test "User Registration"
curl -s -X POST "$API_BASE/users/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User",
    "password": "testpassword123"
  }' | jq '.' || echo "❌ User registration failed"

# Test 4: Client Creation
print_test "Client Creation"
curl -s -X POST "$API_BASE/clients/create" \
  -F "user_id=$TEST_USER_ID" \
  -F "name=Test Client Organization" \
  -F "description=A test client for platform testing" | jq '.' || echo "❌ Client creation failed"

# Test 5: Sub-Client Creation
print_test "Sub-Client Creation"
curl -s -X POST "$API_BASE/sub-clients/create" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Sub-Client",
    "description": "A test sub-client for document processing",
    "client_id": "'$TEST_CLIENT_ID'",
    "contact_email": "<EMAIL>",
    "contact_name": "Jane Smith"
  }' | jq '.' || echo "❌ Sub-client creation failed"

# Test 6: Document Upload and Processing
print_test "Document Upload and Processing"
create_test_file

curl -s -X POST "$API_BASE/files/upload" \
  -F "file=@test_document.txt" \
  -F "client_id=$TEST_CLIENT_ID" \
  -F "sub_client_id=$TEST_SUB_CLIENT_ID" \
  -F "user_id=$TEST_USER_ID" | jq '.' || echo "❌ File upload failed"

# Test 7: File Listing
print_test "File Listing"
curl -s -X GET "$API_BASE/files/$TEST_CLIENT_ID?sub_client_id=$TEST_SUB_CLIENT_ID" | jq '.' || echo "❌ File listing failed"

# Test 8: LLM Content Generation - Summary
print_test "LLM Summary Generation"
curl -s -X POST "$API_BASE/generate/summary" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Summarize the uploaded documents and highlight key points",
    "client_id": "'$TEST_CLIENT_ID'",
    "sub_client_id": "'$TEST_SUB_CLIENT_ID'",
    "additional_params": {
      "user_id": "'$TEST_USER_ID'"
    }
  }' | jq '.' || echo "❌ Summary generation failed"

# Test 9: LLM Content Generation - Email
print_test "LLM Email Generation"
curl -s -X POST "$API_BASE/generate/email" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a professional email to stakeholders about the test results",
    "client_id": "'$TEST_CLIENT_ID'",
    "sub_client_id": "'$TEST_SUB_CLIENT_ID'",
    "additional_params": {
      "user_id": "'$TEST_USER_ID'",
      "recipient_name": "Stakeholder Team",
      "sender_name": "Project Manager"
    }
  }' | jq '.' || echo "❌ Email generation failed"

# Test 10: LLM Content Generation - Action Items
print_test "LLM Action Items Generation"
curl -s -X POST "$API_BASE/generate/action-items" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Extract and prioritize action items from the uploaded documents",
    "client_id": "'$TEST_CLIENT_ID'",
    "sub_client_id": "'$TEST_SUB_CLIENT_ID'",
    "additional_params": {
      "user_id": "'$TEST_USER_ID'"
    }
  }' | jq '.' || echo "❌ Action items generation failed"

# Test 11: Output Listing
print_test "Output Listing"
curl -s -X GET "$API_BASE/outputs/$TEST_CLIENT_ID?sub_client_id=$TEST_SUB_CLIENT_ID" | jq '.' || echo "❌ Output listing failed"

# Test 12: Dashboard Analytics
print_test "Dashboard Analytics"
curl -s -X GET "$API_BASE/dashboard/$TEST_CLIENT_ID" | jq '.' || echo "❌ Dashboard analytics failed"

# Test 13: Client Listing
print_test "Client Listing"
curl -s -X GET "$API_BASE/clients/$TEST_USER_ID" | jq '.' || echo "❌ Client listing failed"

# Test 14: Sub-Client Listing
print_test "Sub-Client Listing"
curl -s -X GET "$API_BASE/sub-clients/$TEST_CLIENT_ID" | jq '.' || echo "❌ Sub-client listing failed"

# Cleanup
echo ""
echo "🧹 Cleaning up test files..."
rm -f test_document.txt

echo ""
echo "✅ Test suite completed!"
echo ""
echo "📊 What was tested:"
echo "  ✓ API health and service availability"
echo "  ✓ User and client management"
echo "  ✓ Document upload and processing"
echo "  ✓ Text extraction and embedding"
echo "  ✓ LLM content generation (summaries, emails, action items)"
echo "  ✓ Data retrieval and analytics"
echo "  ✓ Multi-tenant data isolation"
echo ""
echo "🔍 Check the responses above for any errors or issues."
echo "📈 If all tests passed, your platform is working correctly!"
