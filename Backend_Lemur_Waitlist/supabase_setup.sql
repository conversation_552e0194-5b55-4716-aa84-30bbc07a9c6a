-- Supabase Database Setup for Lemur Multi-Tenant LLM Platform
-- Run these SQL commands in your Supabase SQL editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Create clients table
CREATE TABLE IF NOT EXISTS clients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Create sub_clients table
CREATE TABLE IF NOT EXISTS sub_clients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    contact_email VARCHAR(255),
    contact_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Create files table
CREATE TABLE IF NOT EXISTS files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INTEGER NOT NULL,
    storage_path TEXT NOT NULL,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    sub_client_id UUID REFERENCES sub_clients(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    processed BOOLEAN DEFAULT FALSE,
    extracted_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create meetings table
CREATE TABLE IF NOT EXISTS meetings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    transcript TEXT,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    sub_client_id UUID REFERENCES sub_clients(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    meeting_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create outputs table
CREATE TABLE IF NOT EXISTS outputs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    output_type VARCHAR(100) NOT NULL,
    prompt TEXT,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    sub_client_id UUID REFERENCES sub_clients(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    file_id UUID REFERENCES files(id) ON DELETE SET NULL,
    meeting_id UUID REFERENCES meetings(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_clients_user_id ON clients(user_id);
CREATE INDEX IF NOT EXISTS idx_sub_clients_client_id ON sub_clients(client_id);
CREATE INDEX IF NOT EXISTS idx_files_client_id ON files(client_id);
CREATE INDEX IF NOT EXISTS idx_files_sub_client_id ON files(sub_client_id);
CREATE INDEX IF NOT EXISTS idx_files_user_id ON files(user_id);
CREATE INDEX IF NOT EXISTS idx_meetings_client_id ON meetings(client_id);
CREATE INDEX IF NOT EXISTS idx_meetings_sub_client_id ON meetings(sub_client_id);
CREATE INDEX IF NOT EXISTS idx_outputs_client_id ON outputs(client_id);
CREATE INDEX IF NOT EXISTS idx_outputs_sub_client_id ON outputs(sub_client_id);
CREATE INDEX IF NOT EXISTS idx_outputs_user_id ON outputs(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sub_clients_updated_at BEFORE UPDATE ON sub_clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_files_updated_at BEFORE UPDATE ON files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meetings_updated_at BEFORE UPDATE ON meetings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_outputs_updated_at BEFORE UPDATE ON outputs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE sub_clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE files ENABLE ROW LEVEL SECURITY;
ALTER TABLE meetings ENABLE ROW LEVEL SECURITY;
ALTER TABLE outputs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Users can only see their own data
CREATE POLICY "Users can view own data" ON users FOR SELECT USING (auth.uid()::text = id::text);
CREATE POLICY "Users can update own data" ON users FOR UPDATE USING (auth.uid()::text = id::text);

-- Clients: users can only see their own clients
CREATE POLICY "Users can view own clients" ON clients FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can insert own clients" ON clients FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update own clients" ON clients FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete own clients" ON clients FOR DELETE USING (auth.uid()::text = user_id::text);

-- Sub-clients: users can only see sub-clients of their clients
CREATE POLICY "Users can view own sub-clients" ON sub_clients FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM clients 
        WHERE clients.id = sub_clients.client_id 
        AND clients.user_id::text = auth.uid()::text
    )
);
CREATE POLICY "Users can insert own sub-clients" ON sub_clients FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM clients 
        WHERE clients.id = sub_clients.client_id 
        AND clients.user_id::text = auth.uid()::text
    )
);
CREATE POLICY "Users can update own sub-clients" ON sub_clients FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM clients 
        WHERE clients.id = sub_clients.client_id 
        AND clients.user_id::text = auth.uid()::text
    )
);
CREATE POLICY "Users can delete own sub-clients" ON sub_clients FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM clients 
        WHERE clients.id = sub_clients.client_id 
        AND clients.user_id::text = auth.uid()::text
    )
);

-- Files: users can only see files they uploaded
CREATE POLICY "Users can view own files" ON files FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can insert own files" ON files FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update own files" ON files FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete own files" ON files FOR DELETE USING (auth.uid()::text = user_id::text);

-- Meetings: users can only see meetings they created
CREATE POLICY "Users can view own meetings" ON meetings FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can insert own meetings" ON meetings FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update own meetings" ON meetings FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete own meetings" ON meetings FOR DELETE USING (auth.uid()::text = user_id::text);

-- Outputs: users can only see outputs they generated
CREATE POLICY "Users can view own outputs" ON outputs FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can insert own outputs" ON outputs FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update own outputs" ON outputs FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete own outputs" ON outputs FOR DELETE USING (auth.uid()::text = user_id::text);

-- Insert sample data for testing
INSERT INTO users (id, email, name) VALUES 
    ('demo-user-1', '<EMAIL>', 'Demo User')
ON CONFLICT (email) DO NOTHING;

INSERT INTO clients (id, name, description, user_id) VALUES 
    ('demo-client-1', 'Demo Client Corp', 'A demo client for testing', 'demo-user-1')
ON CONFLICT (id) DO NOTHING;

INSERT INTO sub_clients (id, name, description, client_id, contact_email, contact_name) VALUES 
    ('demo-sub-client-1', 'Demo Sub-Client A', 'First demo sub-client', 'demo-client-1', '<EMAIL>', 'John Doe'),
    ('demo-sub-client-2', 'Demo Sub-Client B', 'Second demo sub-client', 'demo-client-1', '<EMAIL>', 'Jane Smith')
ON CONFLICT (id) DO NOTHING;

-- Create a view for dashboard analytics
CREATE OR REPLACE VIEW client_analytics AS
SELECT 
    c.id as client_id,
    c.name as client_name,
    c.user_id,
    COUNT(DISTINCT sc.id) as sub_client_count,
    COUNT(DISTINCT f.id) as file_count,
    COUNT(DISTINCT o.id) as output_count,
    COUNT(DISTINCT m.id) as meeting_count,
    MAX(f.created_at) as last_file_upload,
    MAX(o.created_at) as last_output_generated
FROM clients c
LEFT JOIN sub_clients sc ON c.id = sc.client_id
LEFT JOIN files f ON c.id = f.client_id
LEFT JOIN outputs o ON c.id = o.client_id
LEFT JOIN meetings m ON c.id = m.client_id
GROUP BY c.id, c.name, c.user_id;

-- Grant permissions on the view
GRANT SELECT ON client_analytics TO authenticated;

COMMENT ON TABLE users IS 'Platform users who own client organizations';
COMMENT ON TABLE clients IS 'B2B customer organizations';
COMMENT ON TABLE sub_clients IS 'Clients of the B2B customers';
COMMENT ON TABLE files IS 'Uploaded files for processing';
COMMENT ON TABLE meetings IS 'Meeting transcripts and recordings';
COMMENT ON TABLE outputs IS 'LLM-generated content (emails, summaries, etc.)';
COMMENT ON VIEW client_analytics IS 'Analytics view for client dashboard';
