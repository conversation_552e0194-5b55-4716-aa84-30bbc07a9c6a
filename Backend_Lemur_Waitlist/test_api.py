#!/usr/bin/env python3
"""
Test script for the Lemur Waitlist API
Run this script to test all API endpoints
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure it's running on port 8000")
        return False

def test_submit_waitlist():
    """Test waitlist submission"""
    print("\n📝 Testing waitlist submission...")
    
    test_data = {
        "email": f"test+{int(time.time())}@example.com",
        "name": "Test User"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/submit",
            headers={"Content-Type": "application/json"},
            json=test_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Waitlist submission successful")
            print(f"   Email: {test_data['email']}")
            print(f"   Response: {result}")
            return True
        else:
            print(f"❌ Waitlist submission failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during submission: {e}")
        return False

def test_duplicate_submission():
    """Test duplicate email detection"""
    print("\n🔄 Testing duplicate email detection...")
    
    # Use a fixed email for duplicate testing
    test_data = {
        "email": "<EMAIL>",
        "name": "Duplicate Test"
    }
    
    try:
        # First submission
        response1 = requests.post(
            f"{BASE_URL}/submit",
            headers={"Content-Type": "application/json"},
            json=test_data
        )
        
        # Second submission (should be detected as duplicate)
        response2 = requests.post(
            f"{BASE_URL}/submit",
            headers={"Content-Type": "application/json"},
            json=test_data
        )
        
        if response2.status_code == 200:
            result = response2.json()
            if "already on our waitlist" in result.get("message", "").lower():
                print("✅ Duplicate detection working")
                print(f"   Response: {result}")
                return True
            else:
                print("⚠️ Duplicate not detected properly")
                print(f"   Response: {result}")
                return False
        else:
            print(f"❌ Duplicate test failed: {response2.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during duplicate test: {e}")
        return False

def test_get_submissions():
    """Test getting all submissions"""
    print("\n📊 Testing get submissions...")
    
    try:
        response = requests.get(f"{BASE_URL}/submissions")
        
        if response.status_code == 200:
            submissions = response.json()
            print(f"✅ Retrieved {len(submissions)} submissions")
            if submissions:
                print(f"   Latest submission: {submissions[-1]}")
            return True
        else:
            print(f"❌ Get submissions failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error getting submissions: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Lemur Waitlist API Test Suite")
    print("=" * 50)
    
    tests = [
        test_health_check,
        test_submit_waitlist,
        test_duplicate_submission,
        test_get_submissions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # Small delay between tests
    
    print("\n" + "=" * 50)
    print(f"📈 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your API is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    print("\n💡 Tips:")
    print("- Make sure the server is running: python main.py")
    print("- Check your .env file for email configuration")
    print("- View API docs at: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
