#!/usr/bin/env python3
"""
Test Supabase connection and basic operations
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_supabase_connection():
    """Test basic Supabase connection"""
    
    # Check if credentials are set
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_ANON_KEY")
    
    print("🔍 Testing Supabase Configuration")
    print("=" * 40)
    
    if not supabase_url:
        print("❌ SUPABASE_URL not set in .env")
        return False
    
    if not supabase_key:
        print("❌ SUPABASE_ANON_KEY not set in .env")
        return False
    
    print(f"✅ SUPABASE_URL: {supabase_url[:30]}...")
    print(f"✅ SUPABASE_ANON_KEY: {supabase_key[:30]}...")
    
    # Try to import and connect to Supabase
    try:
        from supabase import create_client
        print("✅ Supabase library imported successfully")
        
        # Create client
        supabase = create_client(supabase_url, supabase_key)
        print("✅ Supabase client created successfully")
        
        # Test connection by querying users table
        try:
            result = supabase.table("users").select("*").limit(1).execute()
            print(f"✅ Database connection successful")
            print(f"   Users table exists and returned {len(result.data)} rows")
            
            # Try to insert a test user
            try:
                test_user = {
                    "email": "<EMAIL>",
                    "name": "Test Connection User"
                }
                
                insert_result = supabase.table("users").insert(test_user).execute()
                if insert_result.data:
                    print("✅ User insertion successful")
                    user_id = insert_result.data[0]['id']
                    print(f"   Created user with ID: {user_id}")
                    
                    # Clean up - delete the test user
                    supabase.table("users").delete().eq("email", "<EMAIL>").execute()
                    print("✅ Test user cleaned up")
                    
                    return True
                else:
                    print("❌ User insertion failed - no data returned")
                    return False
                    
            except Exception as e:
                print(f"❌ User insertion failed: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Database query failed: {e}")
            print("   This usually means the tables don't exist yet")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import Supabase: {e}")
        print("   Run: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def test_database_tables():
    """Test if all required tables exist"""
    
    try:
        from supabase import create_client
        
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_ANON_KEY")
        supabase = create_client(supabase_url, supabase_key)
        
        print("\n🔍 Testing Database Tables")
        print("=" * 40)
        
        tables_to_test = ["users", "clients", "files", "outputs"]
        
        for table in tables_to_test:
            try:
                result = supabase.table(table).select("*").limit(1).execute()
                print(f"✅ Table '{table}' exists and accessible")
            except Exception as e:
                print(f"❌ Table '{table}' error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database table test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Supabase Connection Test")
    print("=" * 50)
    
    # Test basic connection
    connection_ok = test_supabase_connection()
    
    if connection_ok:
        # Test database tables
        test_database_tables()
        print("\n🎉 Supabase is working correctly!")
        print("\nYou can now test user registration:")
        print('curl -X POST "http://localhost:8000/users/register" \\')
        print('  -H "Content-Type: application/json" \\')
        print('  -d \'{"email": "<EMAIL>", "name": "New Test", "password": "test123"}\'')
    else:
        print("\n❌ Supabase setup needs to be fixed")
        print("\nNext steps:")
        print("1. Check your .env file has correct SUPABASE_URL and SUPABASE_ANON_KEY")
        print("2. Run the simplified SQL schema in Supabase SQL editor")
        print("3. Make sure your Supabase project is fully initialized")
