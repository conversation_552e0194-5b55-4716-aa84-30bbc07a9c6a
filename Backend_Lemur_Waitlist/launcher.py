#!/usr/bin/env python3
"""
Lemur Multi-Tenant LLM Platform Launcher
Starts both FastAPI backend and Streamlit dashboard
"""

import os
import sys
import time
import signal
import subprocess
import threading
import requests
from pathlib import Path

class LemurLauncher:
    def __init__(self):
        self.fastapi_process = None
        self.streamlit_process = None
        self.running = True
        
    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        print("📦 Checking dependencies...")
        
        try:
            import fastapi
            import streamlit
            import supabase
            import openai
            print("   ✅ All dependencies found")
            return True
        except ImportError as e:
            print(f"   ❌ Missing dependency: {e}")
            print("   Installing dependencies...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                             check=True, capture_output=True)
                print("   ✅ Dependencies installed successfully")
                return True
            except subprocess.CalledProcessError:
                print("   ❌ Failed to install dependencies")
                return False
    
    def check_environment(self):
        """Check if environment is properly configured"""
        print("🔍 Checking environment...")
        
        if not Path(".env").exists():
            print("   ❌ .env file not found. Please create it from .env.example")
            return False
        
        # Create necessary directories
        Path("uploads").mkdir(exist_ok=True)
        Path("chroma_db").mkdir(exist_ok=True)
        
        print("   ✅ Environment configured")
        return True
    
    def start_fastapi(self):
        """Start FastAPI server"""
        print("🌐 Starting FastAPI server...")
        
        try:
            self.fastapi_process = subprocess.Popen(
                [sys.executable, "main.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for server to start
            for i in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get("http://localhost:8000/health", timeout=1)
                    if response.status_code == 200:
                        print("   ✅ FastAPI server started on http://localhost:8000")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
            
            print("   ❌ FastAPI server failed to start")
            return False
            
        except Exception as e:
            print(f"   ❌ Error starting FastAPI: {e}")
            return False
    
    def start_streamlit(self):
        """Start Streamlit dashboard"""
        print("📊 Starting Streamlit dashboard...")
        
        try:
            self.streamlit_process = subprocess.Popen(
                [sys.executable, "-m", "streamlit", "run", "dashboard.py", 
                 "--server.headless", "true", "--server.port", "8501"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for dashboard to start
            for i in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get("http://localhost:8501", timeout=1)
                    if response.status_code == 200:
                        print("   ✅ Streamlit dashboard started on http://localhost:8501")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
            
            print("   ⚠️  Streamlit dashboard may still be starting...")
            return True
            
        except Exception as e:
            print(f"   ❌ Error starting Streamlit: {e}")
            return False
    
    def monitor_processes(self):
        """Monitor running processes"""
        while self.running:
            time.sleep(5)
            
            # Check FastAPI
            if self.fastapi_process and self.fastapi_process.poll() is not None:
                print("❌ FastAPI server stopped unexpectedly")
                self.shutdown()
                break
            
            # Check Streamlit
            if self.streamlit_process and self.streamlit_process.poll() is not None:
                print("❌ Streamlit dashboard stopped unexpectedly")
                self.shutdown()
                break
    
    def shutdown(self):
        """Shutdown all services"""
        print("\n🛑 Shutting down services...")
        self.running = False
        
        if self.fastapi_process:
            self.fastapi_process.terminate()
            try:
                self.fastapi_process.wait(timeout=5)
                print("   ✅ FastAPI server stopped")
            except subprocess.TimeoutExpired:
                self.fastapi_process.kill()
                print("   ⚠️  FastAPI server force killed")
        
        if self.streamlit_process:
            self.streamlit_process.terminate()
            try:
                self.streamlit_process.wait(timeout=5)
                print("   ✅ Streamlit dashboard stopped")
            except subprocess.TimeoutExpired:
                self.streamlit_process.kill()
                print("   ⚠️  Streamlit dashboard force killed")
        
        print("👋 Goodbye!")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.shutdown()
        sys.exit(0)
    
    def run(self):
        """Main launcher function"""
        print("🚀 Starting Lemur Multi-Tenant LLM Platform...")
        print("=" * 60)
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Check dependencies and environment
        if not self.check_dependencies():
            return False
        
        if not self.check_environment():
            return False
        
        # Start services
        if not self.start_fastapi():
            return False
        
        if not self.start_streamlit():
            self.shutdown()
            return False
        
        # Display success message
        print("\n🎉 All services are running!")
        print("=" * 60)
        print("📍 Access Points:")
        print("   🌐 FastAPI Backend:     http://localhost:8000")
        print("   📚 API Documentation:   http://localhost:8000/docs")
        print("   📊 Streamlit Dashboard: http://localhost:8501")
        print("\n🧪 Quick Tests:")
        print("   curl http://localhost:8000/health")
        print("   python test_api.py")
        print("\n📖 Features Available:")
        print("   ✅ Waitlist registration with email notifications")
        print("   ✅ User and client management")
        print("   ✅ File upload and processing (PDF, DOCX, Images)")
        print("   ✅ LLM content generation (emails, summaries, action items)")
        print("   ✅ Interactive dashboard and analytics")
        print("\nPress Ctrl+C to stop all services")
        print("=" * 60)
        
        # Start monitoring in a separate thread
        monitor_thread = threading.Thread(target=self.monitor_processes)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # Keep main thread alive
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.shutdown()
        
        return True

def main():
    """Main entry point"""
    launcher = LemurLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
