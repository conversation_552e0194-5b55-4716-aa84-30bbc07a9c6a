#!/usr/bin/env python3
"""
Check all dependencies and imports for the Lemur platform
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_environment_variables():
    """Check if all required environment variables are set"""
    print("🔍 Checking Environment Variables")
    print("=" * 40)
    
    required_vars = {
        'SUPABASE_URL': os.getenv('SUPABASE_URL'),
        'SUPABASE_ANON_KEY': os.getenv('SUPABASE_ANON_KEY'),
        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
        'SMTP_USERNAME': os.getenv('SMTP_USERNAME'),
        'SMTP_PASSWORD': os.getenv('SMTP_PASSWORD'),
    }
    
    for var, value in required_vars.items():
        if value:
            print(f"✅ {var}: {'*' * 20}...{value[-10:] if len(value) > 10 else value}")
        else:
            print(f"❌ {var}: Not set")
    
    return all(required_vars.values())

def check_basic_imports():
    """Check basic Python imports"""
    print("\n🔍 Checking Basic Imports")
    print("=" * 40)
    
    basic_imports = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('pydantic', 'Pydantic'),
        ('dotenv', 'python-dotenv'),
        ('requests', 'Requests'),
    ]
    
    all_good = True
    for module, name in basic_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {e}")
            all_good = False
    
    return all_good

def check_supabase():
    """Check Supabase integration"""
    print("\n🔍 Checking Supabase")
    print("=" * 40)
    
    try:
        from supabase import create_client
        print("✅ Supabase library imported")
        
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_ANON_KEY')
        
        if supabase_url and supabase_key:
            try:
                client = create_client(supabase_url, supabase_key)
                print("✅ Supabase client created")
                
                # Test connection
                result = client.table('users').select('*').limit(1).execute()
                print("✅ Supabase database connection successful")
                return True
            except Exception as e:
                print(f"❌ Supabase connection failed: {e}")
                return False
        else:
            print("❌ Supabase credentials not configured")
            return False
            
    except ImportError as e:
        print(f"❌ Supabase import failed: {e}")
        print("   Run: pip install supabase")
        return False

def check_openai():
    """Check OpenAI integration"""
    print("\n🔍 Checking OpenAI")
    print("=" * 40)
    
    try:
        import openai
        print("✅ OpenAI library imported")
        
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key:
            print(f"✅ OpenAI API key configured: {api_key[:20]}...")
            
            # Test API key
            try:
                openai.api_key = api_key
                # Simple test - just check if we can create a client
                from openai import OpenAI
                client = OpenAI(api_key=api_key)
                print("✅ OpenAI client created successfully")
                return True
            except Exception as e:
                print(f"❌ OpenAI API test failed: {e}")
                return False
        else:
            print("❌ OpenAI API key not configured")
            return False
            
    except ImportError as e:
        print(f"❌ OpenAI import failed: {e}")
        print("   Run: pip install openai")
        return False

def check_file_processing():
    """Check file processing dependencies"""
    print("\n🔍 Checking File Processing")
    print("=" * 40)
    
    file_deps = [
        ('PyPDF2', 'PDF processing'),
        ('docx', 'DOCX processing'),
        ('PIL', 'Image processing'),
        ('pytesseract', 'OCR'),
        ('chromadb', 'Vector database'),
        ('sentence_transformers', 'Embeddings'),
        ('langchain', 'LangChain'),
        ('langchain_openai', 'LangChain OpenAI'),
    ]
    
    all_good = True
    for module, description in file_deps:
        try:
            __import__(module)
            print(f"✅ {description}")
        except ImportError as e:
            print(f"❌ {description}: {e}")
            all_good = False
    
    return all_good

def check_custom_modules():
    """Check our custom modules"""
    print("\n🔍 Checking Custom Modules")
    print("=" * 40)
    
    custom_modules = [
        ('email_templates', 'Email templates'),
        ('database', 'Database operations'),
        ('file_processor', 'File processing'),
        ('llm_service', 'LLM service'),
    ]
    
    all_good = True
    for module, description in custom_modules:
        try:
            __import__(module)
            print(f"✅ {description}")
        except ImportError as e:
            print(f"❌ {description}: {e}")
            all_good = False
    
    return all_good

def main():
    """Run all checks"""
    print("🧪 Lemur Platform Dependency Check")
    print("=" * 50)
    
    checks = [
        ("Environment Variables", check_environment_variables),
        ("Basic Imports", check_basic_imports),
        ("Supabase", check_supabase),
        ("OpenAI", check_openai),
        ("File Processing", check_file_processing),
        ("Custom Modules", check_custom_modules),
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"❌ {name} check failed: {e}")
            results[name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DEPENDENCY CHECK SUMMARY")
    print("=" * 50)
    
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All dependencies are working!")
    else:
        print("\n🔧 To fix issues:")
        print("1. Install missing packages: pip install -r requirements.txt")
        print("2. Check your .env file configuration")
        print("3. Restart the server after fixes")

if __name__ == "__main__":
    main()
