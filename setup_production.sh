#!/bin/bash

# Lemur AI - Production Setup Script
# This script sets up the complete production environment

set -e  # Exit on any error

echo "🚀 Lemur AI - Production Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running from project root
if [ ! -f "setup_production.sh" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_status "Setting up Lemur AI Production Environment..."

# Step 1: Backend Setup
print_status "Step 1: Setting up Clean Backend..."
cd backend_clean

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
print_status "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Check environment variables
if [ ! -f ".env" ]; then
    print_warning ".env file not found in backend_clean/"
    print_status "Creating .env from template..."
    cp .env.example .env
    print_warning "Please update the .env file with your actual API keys and configuration"
fi

# Initialize database
print_status "Initializing database..."
python -c "
from app.core.database import init_db
init_db()
print('✅ Database initialized successfully')
"

print_success "Backend setup complete!"
cd ..

# Step 2: Frontend Setup
print_status "Step 2: Setting up Frontend..."
cd frontend

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."
npm install

# Check environment variables
if [ ! -f ".env" ]; then
    print_warning ".env file not found in frontend/"
    print_status "Creating .env from template..."
    cp .env.example .env
    print_warning "Please update the .env file with your configuration"
fi

print_success "Frontend setup complete!"
cd ..

# Step 3: Verify Setup
print_status "Step 3: Verifying Setup..."

# Check backend health
print_status "Starting backend server for health check..."
cd backend_clean
source venv/bin/activate

# Start backend in background
python main.py &
BACKEND_PID=$!

# Wait for backend to start
sleep 5

# Health check
if curl -s http://localhost:8000/health > /dev/null; then
    print_success "Backend health check passed!"
else
    print_error "Backend health check failed!"
fi

# Stop backend
kill $BACKEND_PID 2>/dev/null || true
cd ..

# Step 4: Create startup scripts
print_status "Step 4: Creating startup scripts..."

# Backend startup script
cat > start_backend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Lemur AI Backend..."
cd backend_clean
source venv/bin/activate
python main.py
EOF

# Frontend startup script
cat > start_frontend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Lemur AI Frontend..."
cd frontend
npm run dev
EOF

# Production startup script
cat > start_production.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Lemur AI Production Environment..."

# Start backend in background
echo "Starting backend..."
cd backend_clean
source venv/bin/activate
python main.py &
BACKEND_PID=$!
cd ..

# Wait for backend to start
sleep 3

# Start frontend
echo "Starting frontend..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ Lemur AI is running!"
echo "📖 Backend API: http://localhost:8000"
echo "🌐 Frontend App: http://localhost:5173"
echo "📚 API Documentation: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap 'echo "Stopping services..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
EOF

# Make scripts executable
chmod +x start_backend.sh
chmod +x start_frontend.sh
chmod +x start_production.sh

print_success "Startup scripts created!"

# Step 5: Final Instructions
print_status "Step 5: Setup Complete!"
echo ""
print_success "🎉 Lemur AI Production Environment Setup Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Update environment variables:"
echo "   - backend_clean/.env (API keys, database config)"
echo "   - frontend/.env (API URLs, feature flags)"
echo ""
echo "2. Start the application:"
echo "   ./start_production.sh    # Start both backend and frontend"
echo "   ./start_backend.sh       # Start only backend"
echo "   ./start_frontend.sh      # Start only frontend"
echo ""
echo "3. Access the application:"
echo "   🌐 Frontend: http://localhost:5173"
echo "   📖 Backend API: http://localhost:8000"
echo "   📚 API Docs: http://localhost:8000/docs"
echo "   🏭 Production Dashboard: http://localhost:5173/production"
echo ""
echo "4. Test the integration:"
echo "   - Register/login a user"
echo "   - Create a client organization"
echo "   - Upload files to build knowledge base"
echo "   - Create meeting bots"
echo "   - Generate AI content"
echo ""
print_success "Happy coding! 🚀"

# Step 6: Optional - Run tests
read -p "Would you like to run the integration tests? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Running integration tests..."
    cd backend_clean
    source venv/bin/activate
    
    # Start backend for testing
    python main.py &
    BACKEND_PID=$!
    sleep 3
    
    # Run tests
    python test_complete_workflow.py
    python test_real_bots.py
    
    # Stop backend
    kill $BACKEND_PID 2>/dev/null || true
    cd ..
    
    print_success "Integration tests complete!"
fi

echo ""
print_success "🎯 Production setup finished successfully!"
