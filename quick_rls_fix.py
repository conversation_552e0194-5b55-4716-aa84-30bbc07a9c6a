#!/usr/bin/env python3
"""
Quick fix to disable RLS policies
"""

import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add Backend_Lemur_Waitlist to path
sys.path.append('./Backend_Lemur_Waitlist')

def disable_rls():
    print("🔧 Disabling RLS policies...")
    
    try:
        from Backend_Lemur_Waitlist.database import get_supabase
        
        supabase = get_supabase()
        if not supabase:
            print("❌ Supabase connection failed")
            return
        
        print("✅ Supabase connection successful")
        
        # Try to disable RLS using SQL
        sql_commands = [
            "ALTER TABLE sub_clients DISABLE ROW LEVEL SECURITY;",
            "ALTER TABLE files DISABLE ROW LEVEL SECURITY;", 
            "ALTER TABLE outputs DISABLE ROW LEVEL SECURITY;"
        ]
        
        for sql in sql_commands:
            try:
                result = supabase.rpc('exec_sql', {'sql': sql}).execute()
                print(f"✅ Executed: {sql}")
            except Exception as e:
                print(f"⚠️  Could not execute {sql}: {e}")
        
        # Test sub-client creation directly
        print("\n🧪 Testing sub-client creation...")
        
        # Get a client ID
        clients_result = supabase.table("clients").select("*").limit(1).execute()
        if not clients_result.data:
            print("❌ No clients found")
            return
            
        client_id = clients_result.data[0]["id"]
        print(f"Using client ID: {client_id}")
        
        sub_client_data = {
            "name": "Test Sub-Client Direct",
            "description": "Testing direct insertion",
            "client_id": client_id,
            "contact_email": "<EMAIL>",
            "contact_name": "Test Contact",
            "is_active": True
        }
        
        try:
            result = supabase.table("sub_clients").insert(sub_client_data).execute()
            if result.data:
                print(f"✅ Sub-client created successfully: {result.data[0]['id']}")
            else:
                print(f"❌ Sub-client creation failed: {result}")
        except Exception as e:
            print(f"❌ Sub-client creation error: {e}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    disable_rls()
